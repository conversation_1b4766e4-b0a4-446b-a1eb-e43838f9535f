import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export type IntakeStage = 'idle' | 'streaming_intake' | 'ingesting' | 'ready';

type AiIntakeState = {
  sessionId: string | null;
  stage: IntakeStage;
  progress: string[];
  insertedCount: number | null;
  error: string | null;
  siiftReady: boolean;
  setSessionId: (id: string | null) => void;
  setStage: (stage: IntakeStage) => void;
  appendProgress: (line: string) => void;
  setInsertedCount: (n: number | null) => void;
  setError: (msg: string | null) => void;
  setSiiftReady: (ready: boolean) => void;
  reset: () => void;
};

export const useAiIntakeStore = create<AiIntakeState>()(
  devtools(
    (set) => ({
  sessionId: null,
  stage: 'idle',
  progress: [],
  insertedCount: null,
  error: null,
  siiftReady: false,
  setSessionId: (id) => set((s) => {
    // Reset progress when starting a new session to prevent endless accumulation
    const shouldResetProgress = id !== s.sessionId && id !== null;
    return shouldResetProgress 
      ? { sessionId: id, progress: [], error: null } 
      : { sessionId: id };
  }),
  setStage: (stage) => set({ stage }),
  appendProgress: (line) => set((s) => {
    // Deduplicate progress messages to prevent infinite accumulation
    // Check last 3 messages to catch immediate duplicates
    const recentMessages = s.progress.slice(-3);
    if (recentMessages.includes(line)) {
      return {}; // No change if message already exists in recent history
    }
    return { progress: [...s.progress, line] };
  }),
  setInsertedCount: (n) => set({ insertedCount: n }),
  setError: (msg) => set({ error: msg }),
  setSiiftReady: (ready) => set({ siiftReady: ready }),
  reset: () => set({ sessionId: null, stage: 'idle', progress: [], insertedCount: null, error: null, siiftReady: false }),
    }),
    {
      name: 'ai-intake-store',
    }
  )
);


