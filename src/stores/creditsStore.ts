import { create } from "zustand";
import { devtools, persist } from "zustand/middleware";

interface CreditsState {
  balance: number;
  isLoading: boolean;
  error: string | null;
  setBalance: (value: number) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

export const useCreditsStore = create<CreditsState>()(
  devtools(
    persist(
      (set) => ({
        balance: 500,
        isLoading: false,
        error: null,
        setBalance: (value: number) => set({ balance: value }),
        setLoading: (isLoading: boolean) => set({ isLoading }),
        setError: (error: string | null) => set({ error }),
      }),
      { name: "credits-store" }
    )
  )
);
