import { create } from "zustand";
import { devtools } from "zustand/middleware";
import type { ChatStore, ChatMessage, ChatSession } from "../types/Chat.types";
import { safeLocalStorage } from "../lib/storage";

export const useChatStore = create<ChatStore>()(
  devtools(
    (set, get) => ({
      messages: [],
      chatSession: null,
      isLoading: false,
      isStreaming: false,
      projectLoadingStates: {},
      projectStreamingStates: {},
      messagesByScope: {},
      currentScopeKey: null,

      // Compute a stable scope key for a given project/topic
      // Key format: p:<projectId> or p:<projectId>|t:<topicSlug>
      getScopeKey: (projectId: string, topicName?: string | null) => {
        const slug = (topicName || "")
          .toString()
          .trim()
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, "")
          .replace(/\s+/g, "-")
          .replace(/-+/g, "-");
        return slug ? `p:${projectId}|t:${slug}` : `p:${projectId}`;
      },

      // Switch current scope and hydrate messages from cache/localStorage
      setScope: (projectId: string, topicName?: string | null) => {
        const key = get().getScopeKey!(projectId, topicName ?? null);
        const prevKey = get().currentScopeKey;
        if (prevKey === key) return;

        // Load from in-memory first, otherwise from localStorage
        const state = get();
        let scopedMessages = state.messagesByScope?.[key];
        if (!scopedMessages) {
          const lsKey = key.includes("|t:")
            ? `chat_messages_${projectId}_topic_${key.split("|t:")[1]}`
            : `chat_messages_${projectId}`;
          scopedMessages = safeLocalStorage.getJSON<ChatMessage[]>(lsKey, []);
        }
        set({ currentScopeKey: key, messages: scopedMessages || [] });
      },

      setMessages: (messages: ChatMessage[]) => {
        const { currentScopeKey, chatSession, messagesByScope } = get();
        const projectId = chatSession?.projectId;
        set({
          messages,
          messagesByScope: {
            ...messagesByScope,
            [currentScopeKey || "default"]: messages,
          },
        });
        // Save to localStorage for the current scope (excluding onboarding messages)
        if (projectId) {
          // Filter out onboarding messages before saving to scope-specific storage
          const nonOnboardingMessages = messages.filter(
            (msg) => !msg.isOnboarding
          );
          if (currentScopeKey && currentScopeKey.includes("|t:")) {
            const slug = currentScopeKey.split("|t:")[1];
            safeLocalStorage.setJSON(
              `chat_messages_${projectId}_topic_${slug}`,
              nonOnboardingMessages
            );
          } else {
            safeLocalStorage.setJSON(
              `chat_messages_${projectId}`,
              nonOnboardingMessages
            );
          }
        }
      },

      addMessage: (message: ChatMessage) => {
        set((state: ChatStore) => {
          const newMessages = [...state.messages, message];
          const updates: Partial<ChatStore> = { messages: newMessages };
          const projectId = state.chatSession?.projectId;
          const scopeKey = state.currentScopeKey || "default";
          updates.messagesByScope = {
            ...(state.messagesByScope || {}),
            [scopeKey]: newMessages,
          };
          // Save to localStorage when adding message (excluding onboarding messages)
          if (projectId) {
            // Filter out onboarding messages before saving to scope-specific storage
            const nonOnboardingMessages = newMessages.filter(
              (msg) => !msg.isOnboarding
            );
            if (scopeKey.includes("|t:")) {
              const slug = scopeKey.split("|t:")[1];
              safeLocalStorage.setJSON(
                `chat_messages_${projectId}_topic_${slug}`,
                nonOnboardingMessages
              );
            } else {
              safeLocalStorage.setJSON(
                `chat_messages_${projectId}`,
                nonOnboardingMessages
              );
            }
          }
          return updates as ChatStore;
        });
      },

      addCtaMessage: (
        text: string,
        cta: { type: "refetch_topics"; label?: string }
      ) => {
        set((state: ChatStore) => {
          const message: ChatMessage = {
            id: `ai-${Date.now()}`,
            user: "siift AI",
            avatar: "",
            message: text,
            timestamp: new Date().toLocaleTimeString([], {
              hour: "2-digit",
              minute: "2-digit",
            }),
            isCurrentUser: false,
            cta,
          };
          const newMessages = [...state.messages, message];
          const updates: Partial<ChatStore> = { messages: newMessages };
          const projectId = state.chatSession?.projectId;
          const scopeKey = state.currentScopeKey || "default";
          updates.messagesByScope = {
            ...(state.messagesByScope || {}),
            [scopeKey]: newMessages,
          };
          if (projectId) {
            if (scopeKey.includes("|t:")) {
              const slug = scopeKey.split("|t:")[1];
              safeLocalStorage.setJSON(
                `chat_messages_${projectId}_topic_${slug}`,
                newMessages
              );
            } else {
              safeLocalStorage.setJSON(
                `chat_messages_${projectId}`,
                newMessages
              );
            }
          }
          return updates as ChatStore;
        });
      },

      updateLastMessage: (content: string) => {
        set((state: ChatStore) => {
          let newMessages;
          if (
            state.messages.length === 0 ||
            state.messages[state.messages.length - 1].isCurrentUser
          ) {
            // Create new AI message
            const newMessage: ChatMessage = {
              id: `ai-${Date.now()}`,
              user: "siift AI",
              avatar: "",
              message: content,
              timestamp: new Date().toLocaleTimeString([], {
                hour: "2-digit",
                minute: "2-digit",
              }),
              isCurrentUser: false,
            };
            newMessages = [...state.messages, newMessage];
          } else {
            // Update last AI message
            const lastMessage = state.messages[state.messages.length - 1];
            const updatedLastMessage = { ...lastMessage, message: content };
            newMessages = [...state.messages.slice(0, -1), updatedLastMessage];
          }

          // Save to localStorage when updating message
          const updates: Partial<ChatStore> = { messages: newMessages };
          const projectId = state.chatSession?.projectId;
          const scopeKey = state.currentScopeKey || "default";
          updates.messagesByScope = {
            ...(state.messagesByScope || {}),
            [scopeKey]: newMessages,
          };
          if (projectId) {
            if (scopeKey.includes("|t:")) {
              const slug = scopeKey.split("|t:")[1];
              safeLocalStorage.setJSON(
                `chat_messages_${projectId}_topic_${slug}`,
                newMessages
              );
            } else {
              safeLocalStorage.setJSON(
                `chat_messages_${projectId}`,
                newMessages
              );
            }
          }

          return updates as ChatStore;
        });
      },

      appendToLastMessage: (contentChunk: string) => {
        set((state: ChatStore) => {
          if (
            state.messages.length === 0 ||
            state.messages[state.messages.length - 1].isCurrentUser
          ) {
            // nothing to append to yet
            return {} as any;
          }
          const last = state.messages[state.messages.length - 1];
          const updatedLast = {
            ...last,
            message: (last.message || "") + contentChunk,
          };
          const newMessages = [...state.messages.slice(0, -1), updatedLast];
          const updates: Partial<ChatStore> = { messages: newMessages };
          const projectId = state.chatSession?.projectId;
          const scopeKey = state.currentScopeKey || "default";
          updates.messagesByScope = {
            ...(state.messagesByScope || {}),
            [scopeKey]: newMessages,
          };
          if (projectId) {
            if (scopeKey.includes("|t:")) {
              const slug = scopeKey.split("|t:")[1];
              safeLocalStorage.setJSON(
                `chat_messages_${projectId}_topic_${slug}`,
                newMessages
              );
            } else {
              safeLocalStorage.setJSON(
                `chat_messages_${projectId}`,
                newMessages
              );
            }
          }
          return updates as any;
        });
      },

      setChatSession: (session: ChatSession | null) => {
        set({ chatSession: session });
      },

      setIsLoading: (isLoading: boolean) => {
        set({ isLoading });
      },

      setIsStreaming: (isStreaming: boolean) => {
        set({ isStreaming });
      },

      setProjectLoading: (projectId: string, isLoading: boolean) => {
        set((state) => ({
          projectLoadingStates: {
            ...state.projectLoadingStates,
            [projectId]: isLoading,
          },
        }));
      },

      setProjectStreaming: (projectId: string, isStreaming: boolean) => {
        set((state) => ({
          projectStreamingStates: {
            ...state.projectStreamingStates,
            [projectId]: isStreaming,
          },
        }));
      },

      getProjectLoading: (projectId: string) => {
        const state = get();
        return state.projectLoadingStates[projectId] || false;
      },

      getProjectStreaming: (projectId: string) => {
        const state = get();
        return state.projectStreamingStates[projectId] || false;
      },

      clearChat: () => {
        set({
          messages: [],
          chatSession: null,
          messagesByScope: {},
          currentScopeKey: null,
        });
      },
    }),
    {
      name: "chat-store",
    }
  )
);
