export type EntryLike =
  | { id?: string | number; status?: string | null | undefined }
  | null
  | undefined;

export type EntryCounts = {
  ideas: number;
  actions: number;
  confirmed: number;
  unproven: number;
  total: number;
};

export function computeEntryCounts(entries: EntryLike[]): EntryCounts {
  const counts: EntryCounts = {
    ideas: 0,
    actions: 0,
    confirmed: 0,
    unproven: 0,
    total: 0,
  };

  if (!Array.isArray(entries) || entries.length === 0) return counts;

  for (const e of entries) {
    if (!e) continue;
    const status = String(e.status || "").toLowerCase();
    if (!status) continue;
    counts.total += 1;
    if (status === "idea") counts.ideas += 1;
    else if (status === "action") counts.actions += 1;
    else if (status === "confirmed") counts.confirmed += 1;
    else if (status === "unproven") counts.unproven += 1;
  }

  return counts;
}

export function computeTopicStatus(
  entries: EntryLike[]
): "idea" | "action" | "confirmed" | "unproven" {
  const list = Array.isArray(entries) ? entries : [];
  if (list.length === 0) return "unproven";
  const allConfirmed = list.every(
    (e) => String(e?.status || "").toLowerCase() === "confirmed"
  );
  if (allConfirmed) return "confirmed";
  const anyAction = list.some(
    (e) => String(e?.status || "").toLowerCase() === "action"
  );
  if (anyAction) return "action";
  const anyIdea = list.some(
    (e) => String(e?.status || "").toLowerCase() === "idea"
  );
  if (anyIdea) return "idea";
  return "unproven";
}

export function mergeEntriesPreferStore<T extends { id?: string | number }>(
  storeEntries: T[] | undefined,
  backendEntries: T[] | undefined,
  options?: { limit?: number }
): T[] {
  const map = new Map<string, T>();
  const normalizeId = (v: string | number | undefined): string =>
    String(v ?? "");
  (backendEntries || []).forEach((e) => map.set(normalizeId(e?.id), e));
  (storeEntries || []).forEach((e) => map.set(normalizeId(e?.id), e));
  const merged = Array.from(map.values());
  if (options?.limit && Number.isFinite(options.limit) && options.limit > 0) {
    return merged.slice(0, options.limit);
  }
  return merged;
}
