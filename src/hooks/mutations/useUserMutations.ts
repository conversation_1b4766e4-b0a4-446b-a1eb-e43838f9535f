"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useClerkAuth } from "@/hooks/useClerkAuth";
import { queryKeys, invalidateQueries } from "@/lib/queryClient";
import { toast } from "sonner";
import type { User } from "@/hooks/queries/useUser";

interface CreateUserData {
  email: string;
  firstName: string;
  lastName: string;
  clerkId: string;
  role?: "user" | "admin";
  status?: "active" | "inactive";
  avatarUrl?: string;
  bio?: string;
  timezone?: string;
  preferences?: {
    notifications?: boolean;
    theme?: "light" | "dark" | "system";
    language?: string;
    [key: string]: any; // Allow additional properties
  };
}

interface UpdateUserData {
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: "user" | "admin";
  status?: "active" | "inactive";
  avatarUrl?: string;
  bio?: string;
  timezone?: string;
  preferences?: {
    notifications?: boolean;
    theme?: "light" | "dark" | "system";
    language?: string;
    [key: string]: any; // Allow additional properties
  };
}

/**
 * Hook to create a new user in the backend
 * Used when syncing Clerk user to backend
 */
export function useCreateUser() {
  const { getToken } = useClerkAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userData: CreateUserData): Promise<User> => {
      const token = await getToken();
      const backendUrl =
        process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";

      const response = await fetch(`${backendUrl}/api/users`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(token && { Authorization: `Bearer ${token}` }),
        },
        body: JSON.stringify({
          ...userData,
          role: userData.role || "user",
          status: userData.status || "active",
          timezone: userData.timezone || "UTC",
          preferences: userData.preferences || {
            notifications: true,
            theme: "system",
            language: "en",
          },
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Backend response error:", {
          status: response.status,
          statusText: response.statusText,
          errorText,
          url: `${backendUrl}/api/users`,
        });

        // If user already exists, try to fetch the existing user
        if (
          response.status === 400 &&
          (errorText.includes("already exists") ||
            errorText.includes("clerk ID already exists"))
        ) {
          console.log("User already exists, fetching existing user...");

          const existingUserResponse = await fetch(
            `${backendUrl}/api/users/clerk/${userData.clerkId}`,
            {
              headers: {
                "Content-Type": "application/json",
                ...(token && { Authorization: `Bearer ${token}` }),
              },
            }
          );

          if (existingUserResponse.ok) {
            const existingUser = await existingUserResponse.json();
            console.log("Found existing user:", existingUser);
            return existingUser;
          } else {
            // If we can't fetch the existing user, that's still okay
            console.log(
              "Could not fetch existing user, but user exists in backend"
            );
            throw new Error("User already exists in backend");
          }
        }

        throw new Error(
          `Failed to create user: ${response.status} ${response.statusText} - ${errorText}`
        );
      }

      return response.json();
    },

    onSuccess: (user, variables) => {
      // Update the user query cache with the user (created or existing)
      queryClient.setQueryData(queryKeys.user(variables.clerkId), user);

      // Invalidate related queries
      invalidateQueries.user(variables.clerkId);

      toast.success("Account synced successfully!");
      console.log("User synced successfully in backend:", user);
    },

    onError: (error: any, variables) => {
      console.error("Failed to create user in backend:", error);
      console.error("User data that failed:", variables);

      // Don't show error toast if user already exists - that's not really an error
      if (error.message && error.message.includes("already exists")) {
        console.log("User already exists in backend - this is expected");
      } else {
        toast.error(`Failed to sync account: ${error.message}`);
      }
    },
  });
}

/**
 * Hook to update user data with optimistic updates
 */
export function useUpdateUser() {
  const { userId, getToken } = useClerkAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      internalUserId,
      userData,
    }: {
      internalUserId: string;
      userData: UpdateUserData;
    }): Promise<User> => {
      const token = await getToken();
      const backendUrl =
        process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3000";

      const response = await fetch(
        `${backendUrl}/api/users/${internalUserId}`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            ...(token && { Authorization: `Bearer ${token}` }),
          },
          body: JSON.stringify({
            ...userData,
            clerkId: userId, // Ensure clerkId is included
          }),
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(
          `Failed to update user: ${response.status} ${errorText}`
        );
      }

      return response.json();
    },

    // Optimistic update
    onMutate: async ({ userData }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.user(userId) });

      // Snapshot previous value
      const previousUser = queryClient.getQueryData<User>(
        queryKeys.user(userId)
      );

      // Optimistically update
      if (previousUser) {
        const optimisticUser = {
          ...previousUser,
          ...userData,
          updatedAt: new Date().toISOString(),
        };

        queryClient.setQueryData(queryKeys.user(userId), optimisticUser);
      }

      return { previousUser };
    },

    // On error, rollback
    onError: (error: any, variables, context) => {
      if (context?.previousUser) {
        queryClient.setQueryData(queryKeys.user(userId), context.previousUser);
      }

      console.error("Failed to update user:", error);
      toast.error("Failed to update profile. Please try again.");
    },

    // Always refetch after error or success
    onSettled: () => {
      invalidateQueries.user(userId);
    },

    onSuccess: (updatedUser) => {
      toast.success("Profile updated successfully!");
      console.log("User updated successfully:", updatedUser);
    },
  });
}

/**
 * Hook to update user by Clerk ID (finds user first, then updates)
 */
export function useUpdateUserByClerkId() {
  const { userId, getToken } = useClerkAuth();
  const queryClient = useQueryClient();
  const updateUser = useUpdateUser();

  return useMutation({
    mutationFn: async (userData: UpdateUserData): Promise<User> => {
      // First, get the user by Clerk ID to find the internal user ID
      const token = await getToken();
      const backendUrl =
        process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3000";

      const getUserResponse = await fetch(
        `${backendUrl}/api/users/clerk/${userId}`,
        {
          headers: {
            "Content-Type": "application/json",
            ...(token && { Authorization: `Bearer ${token}` }),
          },
        }
      );

      if (!getUserResponse.ok) {
        throw new Error(
          "User not found in backend. Please sync your account first."
        );
      }

      const existingUser = await getUserResponse.json();

      // Then update using the internal user ID
      return updateUser.mutateAsync({
        internalUserId: existingUser.id,
        userData,
      });
    },

    onError: (error: any) => {
      console.error("Failed to update user by Clerk ID:", error);
      toast.error(
        error.message || "Failed to update profile. Please try again."
      );
    },
  });
}

/**
 * Hook to sync Clerk user data to backend
 * Combines user existence check and creation
 */
export function useSyncUserToBackend() {
  const { user, userId, email, firstName, lastName } = useClerkAuth();
  const createUser = useCreateUser();

  return useMutation({
    mutationFn: async (): Promise<User> => {
      if (!user || !userId) {
        throw new Error("User not available");
      }

      // Derive robust first/last names if Clerk has not populated them yet
      const fullNameFromClerk = (user.fullName || "").trim();
      const providedFirstName = (firstName || "").trim();
      const providedLastName = (lastName || "").trim();

      let derivedFirstName = providedFirstName;
      let derivedLastName = providedLastName;

      if (!derivedFirstName && fullNameFromClerk) {
        const nameParts = fullNameFromClerk.split(/\s+/);
        derivedFirstName = nameParts[0] || "";
        derivedLastName =
          nameParts.slice(1).join(" ").trim() || derivedLastName;
      }

      if (!derivedFirstName && email) {
        derivedFirstName = email.split("@")[0] || "";
      }

      // Final fallbacks to avoid backend required-field errors
      if (!derivedFirstName) derivedFirstName = "User";
      if (!derivedLastName) derivedLastName = "Account";

      const userData: CreateUserData = {
        email: email || "",
        firstName: derivedFirstName,
        lastName: derivedLastName,
        clerkId: userId,
        role: "user",
        status: "active",
        avatarUrl: user.imageUrl || "",
        bio: "",
        timezone: "UTC",
        preferences: {
          notifications: true,
          theme: "system",
          language: "en",
        },
      };

      return createUser.mutateAsync(userData);
    },

    onError: (error: any) => {
      console.error("Failed to sync user to backend:", error);
      toast.error("Failed to sync account. Please contact support.");
    },
  });
}
