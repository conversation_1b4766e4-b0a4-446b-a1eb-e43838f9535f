"use client";

import { useMutation } from "@tanstack/react-query";

export type FeedbackType = "bug" | "idea" | "praise";

export interface SubmitFeedbackPayload {
  projectId: string;
  type: FeedbackType;
  message: string;
  testimonial: boolean;
  displayName?: string;
  company?: string;
  allowPublic: boolean;
  source?: string;
}

export interface SubmitFeedbackResponse {
  success: boolean;
  data?: unknown;
  error?: string;
}

export function useSubmitFeedback() {
  return useMutation<SubmitFeedbackResponse, Error, SubmitFeedbackPayload>({
    mutationFn: async (payload: SubmitFeedbackPayload) => {
      const res = await fetch("/api/feedback", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const json = (await res
        .json()
        .catch(() => ({}))) as SubmitFeedbackResponse;
      if (!res.ok || json?.success === false) {
        const message =
          json?.error || `Failed to send feedback (${res.status})`;
        throw new Error(message);
      }
      return json;
    },
  });
}

export default useSubmitFeedback;
