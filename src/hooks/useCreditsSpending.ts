"use client";

import { useCallback } from "react";
import { useCreditsStore } from "@/stores/creditsStore";
import { useUpdateUserByClerkId } from "@/hooks/mutations/useUserMutations";
import { useCredits } from "@/hooks/queries/useCredits";
import { useAnalytics } from "@/hooks/useAnalytics";
import { toast } from "sonner";
import { consumeCredits } from "@/lib/credits";

export function useCreditsSpending() {
  const storeBalance = useCreditsStore((s) => s.balance);
  const setBalance = useCreditsStore((s) => s.setBalance);
  const { data: fetchedBalance, refetch } = useCredits();
  const updateUserByClerkId = useUpdateUserByClerkId();
  const { trackCustomEvent } = useAnalytics();

  const effectiveBalance = fetchedBalance ?? storeBalance ?? 0;

  const spendCredits = useCallback(
    async (amount: number, actionDescription?: string): Promise<boolean> => {
      if (amount <= 0) return true; // Free actions always succeed

      const currentBalance = effectiveBalance;

      // Check if user has enough credits
      if (currentBalance < amount) {
        toast.error(
          `Insufficient credits. You need ${amount} credits but only have ${currentBalance}.`
        );
        return false;
      }

      try {
        // Use the new API function to consume credits
        const result = await consumeCredits(amount);
        const newBalance = result.balance;

        // Track the spending event
        trackCustomEvent("credits_spent", {
          amount,
          actionDescription: actionDescription || "unknown",
          previousBalance: currentBalance,
          newBalance,
        });

        // Persist to backend
        await updateUserByClerkId.mutateAsync({
          creditBalance: newBalance,
        } as any);

        // Background refresh to ensure sync
        refetch();

        toast.success(`${amount} credits spent. Balance: ${newBalance}`);
        return true;
      } catch (error) {
        console.error("Failed to spend credits:", error);
        toast.error("Failed to spend credits. Please try again.");
        return false;
      }
    },
    [effectiveBalance, updateUserByClerkId, refetch, trackCustomEvent]
  );

  const canAfford = useCallback(
    (amount: number): boolean => {
      return amount <= 0 || effectiveBalance >= amount;
    },
    [effectiveBalance]
  );

  return {
    balance: effectiveBalance,
    spendCredits,
    canAfford,
    isLoading: updateUserByClerkId.isPending,
  };
}
