import { useNextStep } from "nextstepjs";
import { useEffect, useState } from "react";

export function useWorkspaceTour() {
  const { startNextStep, closeNextStep, isNextStepVisible, currentTour } =
    useNextStep();
  const [hasSeenTour, setHasSeenTour] = useState(false);
  const [tourStartDelay, setTourStartDelay] = useState(2000);

  // Check if user has seen the tour before
  useEffect(() => {
    const tourSeen = localStorage.getItem("workspace-tour-seen");
    if (tourSeen) {
      setHasSeenTour(true);
    }
  }, []);

  // Start the workspace tour
  const startWorkspaceTour = (delay?: number) => {
    const actualDelay = delay ?? tourStartDelay;

    setTimeout(() => {
      startNextStep("workspaceOnboarding");
      setHasSeenTour(true);
      localStorage.setItem("workspace-tour-seen", "true");
    }, actualDelay);
  };

  // Stop the current tour
  const stopWorkspaceTour = () => {
    closeNextStep();
  };

  // Reset tour state (for testing or re-showing)
  const resetTour = () => {
    localStorage.removeItem("workspace-tour-seen");
    setHasSeenTour(false);
  };

  // Auto-start tour if user hasn't seen it
  const autoStartTour = () => {
    if (!hasSeenTour && !isNextStepVisible) {
      startWorkspaceTour();
    }
  };

  return {
    startWorkspaceTour,
    stopWorkspaceTour,
    resetTour,
    autoStartTour,
    isActive: isNextStepVisible,
    hasSeenTour,
    setTourStartDelay,
  };
}
