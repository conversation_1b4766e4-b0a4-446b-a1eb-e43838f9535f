"use client";

import { useQuery } from "@tanstack/react-query";
import { authApi } from "@/lib/api";
import type { Project } from "@/lib/types";

export function useProjects() {
  return useQuery<Project[], Error>({
    queryKey: ["projects"],
    queryFn: () => authApi.getProjects(),
    staleTime: 60_000,
    // Ensure the dashboard/home always shows the latest projects after navigation
    refetchOnMount: "always",
  });
}

export function useProject(projectId: string | undefined) {
  return useQuery<Project, Error>({
    queryKey: ["project", projectId],
    queryFn: () => authApi.getProject(projectId as string),
    enabled: !!projectId,
    staleTime: 60_000,
  });
}
