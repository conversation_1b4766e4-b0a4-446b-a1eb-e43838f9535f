"use client";

import { useQuery } from "@tanstack/react-query";
import type { BusinessSection } from "@/types/BusinessSection.types";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";

export function useBusinessSections(projectId: string | undefined, forceRefresh?: boolean) {
  const { sections: storeSections } = useBusinessSectionStore();
  
  // Check if store already has sections for this project
  const hasDataInStore = storeSections && storeSections.length > 0;
  
  return useQuery<BusinessSection[], Error>({
    queryKey: ["business-sections", projectId],
    queryFn: async () => {
      if (!projectId) return [] as BusinessSection[];
      console.log("[useBusinessSections] Network fetch (store-first strategy)", {
        projectId,
        storeHasData: hasDataInStore,
        storeSectionsCount: storeSections?.length || 0,
        path: `/api/projects/${projectId}/business-sections`,
      });
      const res = await fetch(`/api/projects/${projectId}/business-sections`, {
        cache: "no-store",
      });
      console.log("[useBusinessSections] response", { status: res.status });
      if (!res.ok) throw new Error("Failed to fetch business sections");
      const json = await res.json();
      console.log("[useBusinessSections] raw json", json);
      // Our Next API returns { success: true, data: <backendPayload> }
      // Normalize a few common shapes
      const payload = json?.data ?? json;
      if (Array.isArray(payload)) return payload as BusinessSection[];
      if (Array.isArray(payload?.data))
        return payload.data as BusinessSection[];
      if (Array.isArray(payload?.sections))
        return payload.sections as BusinessSection[];
      if (Array.isArray(payload?.data?.sections))
        return payload.data.sections as BusinessSection[];
      if (Array.isArray(payload?.result))
        return payload.result as BusinessSection[];
      return [];
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    select: (data: any) => {
      console.log("[useBusinessSections] success", {
        count: Array.isArray(data) ? data.length : 0,
        ids: Array.isArray(data) ? data.map((s: BusinessSection) => s.id) : [],
        fromCache: hasDataInStore ? "used store data" : "fetched from network",
      });
      return data as BusinessSection[];
    },
    // Store-first: only fetch if no data in store or explicitly forced
    enabled: !!projectId && (!hasDataInStore || forceRefresh),
    // Business sections change rarely, cache for 30 minutes
    staleTime: 30 * 60 * 1000,
    // Don't refetch on mount if we have cached data
    refetchOnMount: false,
    refetchOnReconnect: true,
    refetchOnWindowFocus: false,
    // Use store data as placeholder while fetching
    placeholderData: hasDataInStore ? storeSections : undefined,
  });
}
