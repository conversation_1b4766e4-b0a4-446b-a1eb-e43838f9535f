"use client";

import { useC<PERSON>kAuth } from "@/hooks/useClerkAuth";
import { cn } from "@/lib/utils";
import { Shield } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";

export function MainNav() {
  const pathname = usePathname() || "";
  const { user } = useClerkAuth();

  // For now, show regular user navigation for everyone
  // You can add role-based navigation later if needed
  if (false) {
    // Temporarily disabled admin check
    return (
      <nav className="flex items-center space-x-6">
        {/* Admin Panel Link */}
        <Link
          href="/admin"
          className={cn(
            "text-sm font-medium transition-colors relative flex items-center gap-1",
            pathname.startsWith("/admin")
              ? "text-[#166534] hover:text-[#166534]"
              : "text-muted-foreground hover:text-[#166534]"
          )}
        >
          <Shield className="h-4 w-4" />
          Admin
          {pathname.startsWith("/admin") && (
            <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-[#166534] rounded-full" />
          )}
        </Link>
      </nav>
    );
  }

  // Regular user navigation
  const userNavigation = [
    {
      name: "Dashboard",
      href: "/user-dashboard",
    },
  ];

  return (
    <nav className="flex items-center space-x-6">
      {userNavigation
        .filter((item) => pathname !== item.href) // Hide dashboard link when already on dashboard
        .map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "text-sm font-medium transition-colors relative",
              pathname === item.href
                ? "text-[#166534] hover:text-[#166534]"
                : "text-muted-foreground hover:text-[#166534]"
            )}
          >
            {item.name}
            {pathname === item.href && (
              <div className="absolute -bottom-1 left-0 right-0 h-0.5 bg-[#166534] rounded-full" />
            )}
          </Link>
        ))}
    </nav>
  );
}
