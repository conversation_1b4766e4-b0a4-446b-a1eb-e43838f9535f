"use client";

import { <PERSON>u, Shield } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useAuth } from "@/hooks/useAuth";
import { cn } from "@/lib/utils";

export function MobileNav() {
  const [open, setOpen] = useState(false);
  const pathname = usePathname() || "";
  const { user } = useAuth();

  // Different navigation for admin vs regular users
  const getNavigation = () => {
    if (user?.role === "admin") {
      return [
        {
          name: "Admin",
          href: "/admin",
          icon: Shield,
        },
      ];
    }

    return [
      {
        name: "Dashboard",
        href: "/user-dashboard",
        icon: undefined,
      },
    ];
  };

  const navigation = getNavigation();

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          className="mr-2 px-0 text-base hover:bg-primary/10 hover:text-primary focus-visible:bg-primary/10 focus-visible:text-primary focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden transition-colors"
        >
          <Menu className="h-6 w-6" />
          <span className="sr-only">Toggle Menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent
        side="left"
        className="pr-0 bg-background border-r border-border"
      >
        {/* Navigation links */}
        <nav className="flex flex-col space-y-3 mt-6">
          {navigation
            .filter((item) => pathname !== item.href) // Hide current page link
            .map((item) => {
              const isActive =
                item.href === "/admin"
                  ? pathname.startsWith("/admin")
                  : pathname === item.href;

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  onClick={() => setOpen(false)}
                  className={cn(
                    "text-sm font-medium transition-colors px-3 py-2 rounded-md relative flex items-center gap-2",
                    isActive
                      ? "text-[#166534] bg-[#166534]/10"
                      : "text-muted-foreground hover:text-[#166534]"
                  )}
                >
                  {item.icon && <item.icon className="h-4 w-4" />}
                  {item.name}
                  {isActive && (
                    <div className="absolute left-0 top-0 bottom-0 w-1 bg-[#166534] rounded-r-full" />
                  )}
                </Link>
              );
            })}
        </nav>
      </SheetContent>
    </Sheet>
  );
}
