"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useAiIntake } from "@/hooks/useAiIntake";
import { useInvalidateSiift } from "@/hooks/queries/useSiift";
import { AlertCircle, CheckCircle, Clock, Zap } from "lucide-react";
import { useState } from "react";

interface AiIntakeProgressProps {
  projectId: string;
  className?: string;
}

export function AiIntakeProgress({
  projectId,
  className,
}: AiIntakeProgressProps) {
  const { sessionId, stage, progress, insertedCount, error, startIntake } =
    useAiIntake();
  const invalidateSiift = useInvalidateSiift();
  const [intakePrompt, setIntakePrompt] = useState("");

  console.log("🔥 [AiIntakeProgress] Current state:", {
    sessionId,
    stage,
    progress,
    insertedCount,
    error,
  });

  const handleStartIntake = async () => {
    if (!intakePrompt.trim()) return;

    try {
      await startIntake(projectId, intakePrompt.trim());
      setIntakePrompt("");
    } catch (error) {
      console.error("Failed to start AI intake:", error);
    }
  };

  const handleInvalidateSiift = () => {
    invalidateSiift(projectId);
  };

  const getStageIcon = () => {
    switch (stage) {
      case "streaming_intake":
        return <Clock className="h-4 w-4 animate-spin text-blue-500" />;
      case "ingesting":
        return <Zap className="h-4 w-4 animate-pulse text-yellow-500" />;
      case "ready":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStageLabel = () => {
    switch (stage) {
      case "streaming_intake":
        return "Processing your input...";
      case "ingesting":
        return "Ingesting insights...";
      case "ready":
        return "Ready";
      default:
        return "Idle";
    }
  };

  const getProgressValue = () => {
    switch (stage) {
      case "streaming_intake":
        return Math.min(progress.length * 10, 50);
      case "ingesting":
        return 75;
      case "ready":
        return 100;
      default:
        return 0;
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          {getStageIcon()}
          <CardTitle className="text-sm">AI Intake</CardTitle>
        </div>
        <CardDescription className="text-xs">{getStageLabel()}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Progress Bar */}
        <Progress value={getProgressValue()} className="h-2" />

        {/* Status Information */}
        {sessionId && (
          <div className="text-xs text-muted-foreground">
            Session: {sessionId.slice(0, 8)}...
          </div>
        )}

        {insertedCount !== null && (
          <div className="text-xs text-green-600">
            Inserted {insertedCount} entries
          </div>
        )}

        {error && (
          <div className="text-xs text-red-500 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            {error}
          </div>
        )}

        {/* Progress Log */}
        {progress.length > 0 && (
          <div className="max-h-24 overflow-y-auto text-xs text-muted-foreground space-y-1">
            {progress.slice(-12).map((line, idx) => (
              <div key={idx} className="truncate">
                {line}
              </div>
            ))}
          </div>
        )}

        {/* Start Intake Controls */}
        {stage === "idle" && (
          <div className="space-y-2">
            <textarea
              value={intakePrompt}
              onChange={(e) => setIntakePrompt(e.target.value)}
              placeholder="Describe your project to start AI intake..."
              className="w-full text-xs p-2 border rounded resize-none"
              rows={2}
            />
            <Button
              onClick={handleStartIntake}
              disabled={!intakePrompt.trim()}
              size="sm"
              variant="neon-green"
              className="w-full text-xs"
            >
              <Zap className="h-3 w-3 mr-1" />
              Start AI Intake
            </Button>
          </div>
        )}

        {/* Refresh Data Button */}
        {stage === "ready" && (
          <Button
            onClick={handleInvalidateSiift}
            variant="outline"
            size="sm"
            className="w-full text-xs"
          >
            Refresh SIIFT Data
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
