"use client";

import { useEffect, useState } from "react";
import { MessageSquare, Send } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { SidebarButton } from "../ui/sidebar-button";

type FeedbackFABProps = {
  projectId: string;
};

export function FeedbackFAB({ projectId }: FeedbackFABProps) {
  const [open, setOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    // Trigger initial zoom-in animation after mount
    const t = setTimeout(() => setMounted(true), 10);
    return () => clearTimeout(t);
  }, []);

  const [form, setForm] = useState({
    message: "",
    review: "",
    hypotheticalPay: "",
    isReview: false,
    displayName: "",
    email: "",
  });

  const reset = () => {
    setForm({
      message: "",
      review: "",
      hypotheticalPay: "",
      isReview: false,
      displayName: "",
      email: "",
    });
    setError(null);
    setSuccess(false);
  };

  const isFormValid = () => {
    if (!form.message.trim()) return false;
    if (!form.review.trim()) return false;
    if (!form.displayName.trim()) return false;
    if (!form.email.trim()) return false;
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!form.message.trim()) {
      setError("Please enter your feedback.");
      return;
    }

    setSubmitting(true);
    setError(null);
    setSuccess(false);
    try {
      const combinedMessage = `Feedback:\n${form.message}\n\nReview:\n${
        form.review
      }\n\nHypothetical payment:\n${form.hypotheticalPay || "-"}`;
      const payload = {
        projectId,
        message: combinedMessage,
        testimonial: form.isReview,
        displayName: form.displayName || undefined,
        email: form.email || undefined,
        allowPublic: form.isReview,
        source: "project",
      } as const;

      const res = await fetch("/api/feedback", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(payload),
      });

      const json = await res.json();
      if (res.ok && json?.success) {
        setSuccess(true);
        reset();
        setOpen(false);
      } else {
        throw new Error(json?.error || "Failed to send feedback");
      }
    } catch (err: any) {
      setError(err?.message || "Failed to send feedback");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Sheet
      open={open}
      onOpenChange={(v) => {
        if (!v) reset();
        setOpen(v);
      }}
    >
      <Tooltip>
        <TooltipTrigger asChild>
          <SheetTrigger asChild>
            <Button
              size="lg"
              className={`fixed bottom-6 right-6 z-50 rounded-full bg-[var(--siift-light-accent)] text-[var(--siift-dark-accent)] hover:bg-[var(--hover-primary)] shadow-lg hover:shadow-xl hover:text-[var(--primary)] border border-[var(--siift-bold-accent)] h-14 w-14 p-0 flex items-center justify-center transition-transform duration-300 ease-out ${
                mounted ? "scale-100" : "scale-0"
              }`}
              aria-label="Send feedback"
            >
              <MessageSquare className="size-5" />
            </Button>
          </SheetTrigger>
        </TooltipTrigger>
        <TooltipContent sideOffset={8}>Send feedback</TooltipContent>
      </Tooltip>

      <SheetContent side="bottom" className="max-w-3xl mx-auto rounded-t-xl">
        <SheetHeader>
          <SheetTitle>Share your feedback</SheetTitle>
        </SheetHeader>

        <form onSubmit={handleSubmit} className="px-4 pb-4 space-y-4">
          <div className="grid gap-2">
            <label className="text-md text-[var(--muted-foreground)]">
              Feedback
            </label>
            <Textarea
              placeholder="Tell us what's working, what's not, or what you'd like to see…"
              value={form.message}
              onChange={(e) =>
                setForm((f) => ({ ...f, message: e.target.value }))
              }
              className="min-h-[120px] text-md"
              required
            />
          </div>

          <div className="grid gap-2">
            <label className="text-md text-[var(--muted-foreground)]">
              Review <span className="text-[var(--destructive)]">*</span>
            </label>
            <Textarea
              placeholder={
                "We’d really appreciate a quick review sharing the value you see, or any other praise you may have 🙏"
              }
              value={form.review}
              onChange={(e) =>
                setForm((f) => ({ ...f, review: e.target.value }))
              }
              className="min-h-[100px] text-md"
              required
            />
          </div>

          <div className="grid gap-2">
            <label className="text-md text-[var(--muted-foreground)]">
              Hypothetically, would you actually pay for this? Why or why not?
            </label>
            <Textarea
              placeholder="Be honest – your input helps us prioritize the right things."
              value={form.hypotheticalPay}
              onChange={(e) =>
                setForm((f) => ({ ...f, hypotheticalPay: e.target.value }))
              }
              className="min-h-[80px] text-md"
            />
          </div>

          <div
            className="flex items-start gap-3 rounded-md border border-[var(--border)] p-3 cursor-pointer hover:bg-[var(--muted)]/50 transition-colors"
            onClick={() => {
              setForm((f) => ({
                ...f,
                isReview: !f.isReview,
              }));
            }}
          >
            <input
              id="isReview"
              type="checkbox"
              checked={form.isReview}
              onChange={(e) =>
                setForm((f) => ({ ...f, isReview: e.target.checked }))
              }
              className="mt-1 h-4 w-4 accent-[var(--primary)] cursor-pointer"
            />
            <label
              htmlFor="isReview"
              onClick={() => {
                setForm((f) => ({
                  ...f,
                  isReview: !f.isReview,
                }));
              }}
              className="text-md leading-6 cursor-pointer flex-1"
            >
              I consent to my review being shared publicly by siift
            </label>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="grid gap-2">
              <label className="text-md text-[var(--muted-foreground)]">
                Your name <span className="text-[var(--destructive)]">*</span>
              </label>
              <Input
                placeholder="Jane Doe"
                value={form.displayName}
                onChange={(e) =>
                  setForm((f) => ({ ...f, displayName: e.target.value }))
                }
                required
                className={
                  !form.displayName.trim()
                    ? "border-[var(--destructive)] text-md"
                    : "text-md"
                }
              />
            </div>
            <div className="grid gap-2">
              <label className="text-md text-[var(--muted-foreground)]">
                Email address{" "}
                <span className="text-[var(--destructive)]">*</span>
              </label>
              <Input
                type="email"
                placeholder="<EMAIL>"
                value={form.email}
                onChange={(e) =>
                  setForm((f) => ({ ...f, email: e.target.value }))
                }
                required
                className={
                  !form.email.trim()
                    ? "border-[var(--destructive)] text-md"
                    : "text-md"
                }
              />
            </div>
          </div>

          {error && (
            <div className="text-[var(--destructive)] text-sm">{error}</div>
          )}

          {success && (
            <div className="text-[var(--primary)] text-sm">
              Thanks for your feedback!
            </div>
          )}

          <SheetFooter className="p-0">
            <div className="ml-auto flex items-center gap-2">
              <SheetClose asChild>
                <SidebarButton
                  variant="outline"
                  type="button"
                  size="md"
                  className="text-sm"
                >
                  Cancel
                </SidebarButton>
              </SheetClose>
              <SidebarButton
                type="submit"
                hoverColor="grey"
                hoverScale={true}
                trailing={<Send className="size-4" />}
                size="md"
                className="bg-[var(--primary)]/10   "
                showBorder={true}
                disabled={submitting || !isFormValid()}
              >
                {submitting ? "Sending…" : "Send feedback"}
              </SidebarButton>
            </div>
          </SheetFooter>
        </form>
      </SheetContent>
    </Sheet>
  );
}

export default FeedbackFAB;
