"use client";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { toast } from "sonner";
import { ProjectInputSection } from "@/components/shared/ProjectInputSection";
import { DottedBackground } from "@/components/ui/dotted-background";

const ALLOWED_SHA256_HEX = new Set<string>([
  "f4340be4842ad8dc1b863a3386dcf0015abc063f60c76e495421407768a7e31f",
  "d1d6858aafb43634d2758632fa5d4bd9c4a4a54cab0989d4f788c0871a1d0046",
  "4376c64a6adc8d0e6a64e0b24b2e9efb29ce5e62c42a6fa71ed855df56a470d5",
  "4efba11ebae3d211946b5e12d61909a64753a0a5f6d76995f0664e0db3a6a039",
]);

function bytesToHexString(bytes: ArrayBuffer): string {
  const byteArray = new Uint8Array(bytes);
  const hexCodes: string[] = [];
  for (let i = 0; i < byteArray.length; i += 1) {
    const hexCode = byteArray[i].toString(16).padStart(2, "0");
    hexCodes.push(hexCode);
  }
  return hexCodes.join("");
}

async function sha256Hex(input: string): Promise<string> {
  const encoder = new TextEncoder();
  const data = encoder.encode(input);
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  return bytesToHexString(hashBuffer);
}

interface BetaAccessGateProps {
  mode?: "landing" | "auth";
  onUnlock?: () => void;
}

export function BetaAccessGate({
  mode = "landing",
  onUnlock,
}: BetaAccessGateProps) {
  const router = useRouter();
  const [password, setPassword] = useState<string>("");
  const [isVerifying, setIsVerifying] = useState<boolean>(false);
  const [hasAccess, setHasAccess] = useState<boolean>(false);

  useEffect(() => {
    const cookies = document.cookie.split("; ");
    const betaCookie = cookies.find((c) => c.startsWith("beta_access="));
    const betaValue = betaCookie?.split("=")[1];
    setHasAccess(betaValue === "1");
  }, []);

  const canSubmit = useMemo(
    () => password.trim().length > 0 && !isVerifying,
    [password, isVerifying]
  );

  const setBetaCookieAndRedirect = useCallback(() => {
    const maxAgeDays = 7;
    const maxAgeSeconds = maxAgeDays * 24 * 60 * 60;
    const cookie = `beta_access=1; Path=/; Max-Age=${maxAgeSeconds}; SameSite=Lax`;
    // Avoid always setting Secure so it also works on localhost during development
    document.cookie = cookie;
    if (mode === "auth") {
      if (onUnlock) onUnlock();
      setHasAccess(true);
    } else {
      router.push("/auth/login");
    }
  }, [router, mode, onUnlock]);

  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault();
      if (!canSubmit) return;
      try {
        setIsVerifying(true);
        const hash = await sha256Hex(password.trim());
        if (ALLOWED_SHA256_HEX.has(hash)) {
          setBetaCookieAndRedirect();
        } else {
          toast.error("Invalid access code. Please try again.");
        }
      } catch (err) {
        console.error("Beta access verification failed:", err);
        toast.error("Something went wrong. Please try again.");
      } finally {
        setIsVerifying(false);
      }
    },
    [canSubmit, password, setBetaCookieAndRedirect]
  );

  if (hasAccess) {
    if (mode === "auth") return null;
    return <ProjectInputSection variant="landing" showFeatureBadges />;
  }

  return (
    <div className="relative min-h-screen flex items-center justify-center bg-background overflow-hidden">
      {/* Dotted Background Pattern */}
      <DottedBackground
        fadeEdge={20}
        dotSizes={[1, 1.5, 2]}
        spacing={25}
        dotsPerRow={8}
        opacity={0.02}
        darkOpacity={0.15}
      />

      {/* Noise Texture Overlay */}
      <div
        className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
        style={{
          backgroundImage:
            "url(\"data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")",
          backgroundSize: "256px 256px",
        }}
      />

      {/* Subtle Gradient Overlay (brand) */}
      <div className="absolute inset-0 bg-gradient-to-br from-[var(--siift-mid-accent)]/20 via-transparent to-[var(--siift-mid-accent)]/8" />

      {/* Card */}
      <div className="relative z-10 w-full max-w-sm mx-auto">
        <form
          onSubmit={handleSubmit}
          className="group relative bg-card/80 hover:bg-card/90 backdrop-blur border rounded-2xl p-6 space-y-4 shadow-xl overflow-hidden transition-all duration-300"
        >
          {/* Gradient overlay to match dashboard style */}
          <div className="absolute inset-0 bg-gradient-to-br from-[var(--primary)]/5 via-transparent to-[var(--primary-light)]/5 pointer-events-none" />

          <div className="relative z-10">
            <div className="text-center space-y-2 mb-4">
              <h2 className="text-xl font-semibold">Beta access required</h2>
              <p className="text-sm text-muted-foreground">
                Enter the beta access code to continue to sign in.
              </p>
            </div>

            <div className="space-y-3">
              <Input
                type="password"
                autoComplete="off"
                placeholder="Enter access code"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full bg-background/50 backdrop-blur focus:ring-[var(--primary)]/20 focus:border-[var(--primary)] transition-all duration-300 hover:shadow-lg"
              />
              <Button
                type="submit"
                disabled={!canSubmit}
                className="w-full bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)] shadow-lg transition-all duration-300"
              >
                {isVerifying ? "Verifying..." : "Unlock beta"}
              </Button>
            </div>

            <div className="mt-3 text-xs text-muted-foreground text-center">
              Your access is stored for 7 days on this device.
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export default BetaAccessGate;
