"use client";

import { AdminSidebar } from "@/components/admin-sidebar";
import { AdminRoute } from "@/components/auth/admin-route";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { usePathname } from "next/navigation";
import React from "react";

interface AdminLayoutProps {
  children: React.ReactNode;
}

// Helper function to generate breadcrumbs from pathname
const getBreadcrumbs = (pathname: string) => {
  const segments = pathname.split("/").filter(Boolean);
  const breadcrumbs = [];

  if (segments.length > 1) {
    breadcrumbs.push({
      title: "Admin",
      href: "/admin",
    });

    if (segments[1] === "analytics") {
      breadcrumbs.push({
        title: "Analytics",
        href: "/admin/analytics",
      });

      if (segments[2]) {
        const pageTitle = segments[2]
          .split("-")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        breadcrumbs.push({
          title: pageTitle,
          href: pathname,
          isCurrentPage: true,
        });
      }
    } else if (segments[1] === "agent") {
      breadcrumbs.push({
        title: "Agent Analytics",
        href: "/admin/agent",
      });

      if (segments[2]) {
        const pageTitle = segments[2]
          .split("-")
          .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
          .join(" ");
        breadcrumbs.push({
          title: pageTitle,
          href: pathname,
          isCurrentPage: true,
        });
      }
    }
  }

  return breadcrumbs;
};

export function AdminLayout({ children }: AdminLayoutProps) {
  const pathname = usePathname();
  const breadcrumbs = getBreadcrumbs(pathname || "/");

  return (
    <AdminRoute>
      <SidebarProvider>
        <AdminSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator
                orientation="vertical"
                className="mr-2 data-[orientation=vertical]:h-4"
              />
              <Breadcrumb>
                <BreadcrumbList>
                  {breadcrumbs.map((breadcrumb, index) => (
                    <React.Fragment key={breadcrumb.href}>
                      <BreadcrumbItem
                        className={index === 0 ? "hidden md:block" : ""}
                      >
                        {breadcrumb.isCurrentPage ? (
                          <BreadcrumbPage>{breadcrumb.title}</BreadcrumbPage>
                        ) : (
                          <BreadcrumbLink href={breadcrumb.href}>
                            {breadcrumb.title}
                          </BreadcrumbLink>
                        )}
                      </BreadcrumbItem>
                      {index < breadcrumbs.length - 1 && (
                        <BreadcrumbSeparator className="hidden md:block" />
                      )}
                    </React.Fragment>
                  ))}
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">{children}</div>
        </SidebarInset>
      </SidebarProvider>
    </AdminRoute>
  );
}
