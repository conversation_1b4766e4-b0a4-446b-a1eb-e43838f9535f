"use client";

import {
  BarChart3,
  Bell,
  Brain,
  Clock,
  FileBarChart,
  FileText,
  FolderOpen,
  Globe,
  Key,
  Lock,
  Monitor,
  Server,
  Settings,
  Shield,
  User,
  Users,
} from "lucide-react";
import { useRouter } from "next/navigation";
import * as React from "react";

import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import { TeamSwitcher } from "@/components/team-switcher";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useAuth } from "@/hooks/useAuth";

// Admin dashboard navigation data
const getAdminNavData = (user: any) => ({
  user: {
    name: user?.name || "Admin",
    email: user?.email || "",
    avatar: user?.avatar || "",
  },
  teams: [
    {
      name: "siift Admin",
      logo: Shield,
      plan: "Admin Panel",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "/admin/analytics",
      icon: BarChart3,
      isActive: true,
      items: [
        {
          title: "Overview",
          url: "/admin/analytics/overview",
        },
        {
          title: "User Analytics",
          url: "/admin/analytics/users",
        },
        {
          title: "Activity Metrics",
          url: "/admin/analytics/activity-metrics",
        },
        {
          title: "Revenue Reports",
          url: "/admin/analytics/revenue",
        },
        {
          title: "Performance",
          url: "/admin/analytics/performance",
        },
        {
          title: "Feedbacks",
          url: "/admin/analytics/feedbacks",
        },
      ],
    },
    {
      title: "Profile",
      url: "/admin/profile",
      icon: User,
    },
    {
      title: "Settings",
      url: "/admin/settings-tab",
      icon: Settings,
    },
    {
      title: "Notifications",
      url: "/admin/notifications",
      icon: Bell,
    },
    {
      title: "Projects",
      url: "/admin/projects",
      icon: FolderOpen,
      items: [
        {
          title: "All Projects",
          url: "/admin/projects/all",
        },
        {
          title: "Create New",
          url: "/admin/projects/create",
        },
      ],
    },
    {
      title: "Recent",
      url: "/admin/recent",
      icon: Clock,
    },
    {
      title: "User Management",
      url: "/admin/users",
      icon: Users,
      items: [
        {
          title: "All Users",
          url: "/admin/users/all",
        },
        {
          title: "Active Users",
          url: "/admin/users/active",
        },
        {
          title: "Pending Approval",
          url: "/admin/users/pending",
        },
        {
          title: "User Roles",
          url: "/admin/users/roles",
        },
        {
          title: "Permissions",
          url: "/admin/users/permissions",
        },
      ],
    },
    {
      title: "Agent Analytics",
      url: "/admin/agent",
      icon: Brain,
      items: [
        {
          title: "Agent Calls",
          url: "/admin/agent/calls",
        },
        {
          title: "Usage Statistics",
          url: "/admin/agent/usage-stats",
        },
        {
          title: "Token Trends",
          url: "/admin/agent/token-trends",
        },
        {
          title: "Performance",
          url: "/admin/agent/performance",
        },
      ],
    },
    {
      title: "Reports",
      url: "/admin/reports",
      icon: FileText,
      items: [
        {
          title: "System Reports",
          url: "/admin/reports/system",
        },
        {
          title: "User Reports",
          url: "/admin/reports/users",
        },
        {
          title: "Activity Reports",
          url: "/admin/reports/activity",
        },
        {
          title: "Export Data",
          url: "/admin/reports/export",
        },
      ],
    },
    {
      title: "System",
      url: "/admin/system",
      icon: Monitor,
      items: [
        {
          title: "Health Check",
          url: "/admin/system/health",
          icon: Shield,
        },
        {
          title: "Server Status",
          url: "/admin/system/status",
          icon: Server,
        },
        {
          title: "Logs",
          url: "/admin/system/logs",
          icon: FileBarChart,
        },
        {
          title: "Maintenance",
          url: "/admin/system/maintenance",
          icon: Settings,
        },
      ],
    },
    {
      title: "Settings",
      url: "/admin/settings",
      icon: Settings,
      items: [
        {
          title: "General",
          url: "/admin/settings/general",
          icon: Settings,
        },
        {
          title: "Security",
          url: "/admin/settings/security",
          icon: Lock,
        },
        {
          title: "Notifications",
          url: "/admin/settings/notifications",
          icon: Bell,
        },
        {
          title: "API Keys",
          url: "/admin/settings/api-keys",
          icon: Key,
        },
        {
          title: "Integrations",
          url: "/admin/settings/integrations",
          icon: Globe,
        },
      ],
    },
  ],
});

export function AdminSidebar({
  ...props
}: React.ComponentProps<typeof Sidebar>) {
  const { user, logout } = useAuth();
  const router = useRouter();

  const data = getAdminNavData(user);

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <TeamSwitcher teams={data.teams} />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
