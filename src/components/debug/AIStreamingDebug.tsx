"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAiIntake } from '@/hooks/useAiIntake';

export function AIStreamingDebug() {
  const [projectId, setProjectId] = useState('test-project-123');
  const [message, setMessage] = useState('This is a test project for debugging AI intake');
  const [logs, setLogs] = useState<string[]>([]);
  const { sessionId, stage, progress, insertedCount, error, startIntake } = useAiIntake();

  const addLog = (log: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${log}`]);
  };

  const testSessionCreation = async () => {
    addLog('Testing session creation...');
    try {
      const resp = await fetch('/api/ai-chat/sessions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ projectId }),
      });
      addLog(`Session response: ${resp.status} ${resp.statusText}`);
      const data = await resp.json();
      addLog(`Session data: ${JSON.stringify(data)}`);
    } catch (error) {
      addLog(`Session error: ${error}`);
    }
  };

  const testChatMessage = async () => {
    if (!sessionId) {
      addLog('No session ID available. Create session first.');
      return;
    }
    addLog(`Testing chat message to session: ${sessionId}`);
    try {
      const resp = await fetch(`/api/ai-chat/sessions/${sessionId}/chat`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          content: message,
          stage: 'coaching',
          // topic_name: 'audience' // uncomment to test topic-specific coaching
        }),
      });
      addLog(`Chat response: ${resp.status} ${resp.statusText}`);
      const data = await resp.text();
      addLog(`Chat data: ${data.slice(0, 200)}...`);
    } catch (error) {
      addLog(`Chat error: ${error}`);
    }
  };

  const testFullIntake = async () => {
    addLog('Testing full AI intake...');
    try {
      await startIntake(projectId, message);
      addLog('Intake started successfully');
    } catch (error) {
      addLog(`Intake error: ${error}`);
    }
  };

  const clearLogs = () => setLogs([]);

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>AI Streaming Debug</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Project ID:</label>
            <input
              type="text"
              value={projectId}
              onChange={(e) => setProjectId(e.target.value)}
              className="w-full p-2 border rounded"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Test Message:</label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="w-full p-2 border rounded"
              rows={3}
            />
          </div>

          <div className="flex gap-2 flex-wrap">
            <Button onClick={testSessionCreation} variant="outline" size="sm">
              Test Session Creation
            </Button>
            <Button onClick={testChatMessage} variant="outline" size="sm" disabled={!sessionId}>
              Test Chat Message
            </Button>
            <Button onClick={testFullIntake} variant="outline" size="sm">
              Test Full Intake
            </Button>
            <Button onClick={clearLogs} variant="outline" size="sm">
              Clear Logs
            </Button>
          </div>

          <div className="space-y-2">
            <div className="text-sm">
              <strong>Status:</strong> {stage} | 
              <strong>Session:</strong> {sessionId || 'none'} | 
              <strong>Progress:</strong> {progress.length} items
            </div>
            {error && (
              <div className="text-red-500 text-sm">
                <strong>Error:</strong> {error}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Debug Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-100 p-3 rounded max-h-64 overflow-y-auto text-xs font-mono">
            {logs.length === 0 ? (
              <div className="text-gray-500">No logs yet. Run a test to see output.</div>
            ) : (
              logs.map((log, idx) => (
                <div key={idx} className="mb-1">
                  {log}
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {progress.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>AI Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-1 text-xs">
              {progress.map((item, idx) => (
                <div key={idx} className="p-1 bg-blue-50 rounded">
                  {item}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}