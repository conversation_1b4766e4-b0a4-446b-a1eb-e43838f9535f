"use client";

import * as Dialog from "@radix-ui/react-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";

export interface ActionSuggestion {
  key: string;
  title: string;
  fullContent?: string; // Full prompt with actual idea text
  type: string;
  credits: number;
  description: string;
  icon?: React.ReactNode;
}

interface ActionSuggestionDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  suggestion: ActionSuggestion | null;
  onRun: () => void;
}

export function ActionSuggestionDialog({
  open,
  onOpenChange,
  suggestion,
  onRun,
}: ActionSuggestionDialogProps) {
  return (
    <Dialog.Root open={open} onOpenChange={onOpenChange}>
      <Dialog.Portal>
        <Dialog.Overlay className="fixed inset-0 bg-[var(--background)]/30 backdrop-blur-[2px] z-50" />
        <Dialog.Content className="fixed left-1/2 bottom-0 -translate-x-1/2 z-50 w-[96vw] sm:w-[80vw] lg:w-1/2 max-w-3xl outline-none">
          <Card className="border  border-[var(--siift-light-mid)]/50  bg-[var(--card)] text-[var(--card-foreground)] shadow-xl rounded-t-xl">
            <CardContent className="p-5">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-2">
                  {suggestion?.icon}
                  <Dialog.Title className="text-lg font-semibold">
                    {suggestion?.title}
                  </Dialog.Title>
                </div>
                <Dialog.Close asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <X className="h-4 w-4" />
                  </Button>
                </Dialog.Close>
              </div>

              <div className="flex items-center gap-2 mb-3">
                <Badge>{suggestion?.type}</Badge>
                <Badge variant="secondary">{suggestion?.credits} credits</Badge>
              </div>

              <p className="text-sm mb-6 opacity-90">
                {suggestion?.description}
              </p>

              <div className="flex items-center justify-end gap-2">
                <Dialog.Close asChild>
                  <Button variant="outline">Cancel</Button>
                </Dialog.Close>
                <Button onClick={onRun}>Run</Button>
              </div>
            </CardContent>
          </Card>
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
}
