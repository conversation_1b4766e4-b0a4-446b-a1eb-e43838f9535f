"use client";

import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import { Shu<PERSON>, Edit3, Brain, Globe, Database, Plus } from "lucide-react";
import { useState, useEffect } from "react";
import type { ActionSuggestion } from "./ActionSuggestionDialog";
import { apiClient } from "@/lib/apiClient";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>onte<PERSON>,
  <PERSON>etHeader,
  <PERSON>etT<PERSON>le,
  Sheet<PERSON>ooter,
} from "@/components/ui/sheet";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { SidebarButton } from "@/components/ui/sidebar-button";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { CoinIcon } from "../../ui/icons";
import { useCreditsSpending } from "@/hooks/useCreditsSpending";

type CategoryKey = "web" | "internal" | "instructions" | "manual";

const CATEGORY_OPTIONS: Record<
  CategoryKey,
  { label: string; credit: number; icon: React.ComponentType<any> }
> = {
  web: { label: "Web Search", credit: 5, icon: Globe },
  internal: { label: "Internal Check", credit: 2, icon: Database },
  instructions: { label: "Instructions", credit: 1, icon: Brain },
  manual: { label: "Manual", credit: 0, icon: Edit3 },
};

// Action object structure for creation
interface ActionItem {
  id: string;
  type: string;
  content: string;
  credit: number;
  progress: number; // 0-100 percentage
  status: string;
  results: any[];
}

// Helper to get category meta by key
const getCategoryMeta = (key: CategoryKey) => CATEGORY_OPTIONS[key];

// Ensure we always map suggestion keys to a valid CategoryKey
const parseCategoryKey = (key: string): CategoryKey => {
  if (!key) return "instructions";
  if (key.startsWith("web")) return "web";
  if (key.startsWith("internal")) return "internal";
  if (key.startsWith("instructions")) return "instructions";
  if (key.startsWith("manual")) return "manual";
  // Streaming or unknown → default to instructions
  return "instructions";
};

// Infer category key from various backend fields
const inferCategoryFromRaw = (raw: any, fallbackText?: string): CategoryKey => {
  const rawType: string = (raw?.category || raw?.type || "")
    .toString()
    .toLowerCase();
  const tags: string[] = Array.isArray(raw?.tags) ? raw.tags : [];
  const text: string = (
    raw?.title ||
    raw?.text ||
    raw?.content ||
    fallbackText ||
    ""
  )
    .toString()
    .toLowerCase();

  if (
    rawType.startsWith("web") ||
    tags.includes("web") ||
    text.includes("research") ||
    text.includes("find")
  ) {
    return "web";
  }
  if (
    rawType.startsWith("instructions") ||
    tags.includes("instructions") ||
    text.includes("plan") ||
    text.includes("step")
  ) {
    return "instructions";
  }
  if (
    rawType.startsWith("internal") ||
    tags.includes("internal") ||
    text.includes("internal") ||
    text.includes("doc")
  ) {
    return "internal";
  }
  if (rawType.startsWith("manual")) {
    return "manual";
  }
  return "instructions";
};

// Normalize any backend suggestion object/primitive to ActionSuggestion
const normalizeBackendSuggestion = (
  raw: any,
  index: number,
  detailTitle?: string
): ActionSuggestion => {
  // Allow raw to be a string (title) or structured object
  const isPrimitive = typeof raw === "string";
  const baseText = isPrimitive
    ? String(raw)
    : String(raw?.title || raw?.text || raw?.content || "");
  const category = inferCategoryFromRaw(raw, baseText);
  const meta = getCategoryMeta(category);

  const fullContent = isPrimitive
    ? baseText
    : String(raw?.fullContent || raw?.content || raw?.text || baseText);

  const idPart =
    !isPrimitive && raw?.id ? String(raw.id) : `${category}-${index}`;
  const credits =
    Number(!isPrimitive && (raw?.credits ?? raw?.credit)) || meta.credit;
  const description = !isPrimitive
    ? String(
        raw?.description ||
          (detailTitle
            ? `AI-generated suggestion based on "${detailTitle.trim()}"`
            : "AI-generated suggestion")
      )
    : detailTitle
    ? `AI-generated suggestion based on "${detailTitle.trim()}"`
    : "AI-generated suggestion";

  return {
    key: idPart,
    title: baseText,
    fullContent,
    type: meta.label,
    credits,
    description,
    icon: <meta.icon className="h-4 w-4" />,
  };
};

function buildSuggestionsFromMetadata(
  detail: BusinessItemDetail
): ActionSuggestion[] {
  // Extract suggestions from entry metadata
  const metadata = (detail as any)?.metadata;
  const suggestedActions = metadata?.suggested_actions || [];

  if (!Array.isArray(suggestedActions) || suggestedActions.length === 0) {
    return []; // No suggestions available yet
  }

  return suggestedActions
    .slice(0, 3)
    .map((action: any, index: number) =>
      normalizeBackendSuggestion(action, index, detail.title)
    );
}

// Fallback suggestions when no real suggestions are available
function buildFallbackSuggestions(
  detail: BusinessItemDetail
): ActionSuggestion[] {
  const subject = detail.title?.trim() || "this idea";
  const pool: Array<{
    key: CategoryKey;
    shortText: string;
    fullText: string;
    description: string;
  }> = [
    {
      key: "instructions",
      shortText: `Draft a step-by-step plan to validate this idea with measurable outcomes`,
      fullText: `Draft a step-by-step plan to validate "${subject}" with measurable outcomes`,
      description:
        "Generates an in-depth, structured plan grounded in best practices and research.",
    },
    {
      key: "web",
      shortText: `Find the top 5 recent resources that address this idea and summarize takeaways`,
      fullText: `Find the top 5 recent resources that address "${subject}" and summarize takeaways`,
      description:
        "Searches the web for up-to-date guidance and compiles a concise summary.",
    },
    {
      key: "internal",
      shortText: `Check internal docs for prior work or decisions related to this idea`,
      fullText: `Check internal docs for prior work or decisions related to "${subject}"`,
      description:
        "Surfaces institutional knowledge to avoid duplicating past efforts.",
    },
  ];

  const result: ActionSuggestion[] = [];

  const shuffled = [...pool].sort(() => Math.random() - 0.5);

  for (let i = 0; i < 3; i++) {
    const item = shuffled[i % shuffled.length];

    const meta = getCategoryMeta(item.key);

    result.push({
      key: item.key,
      title: item.shortText,
      fullContent: item.fullText,
      type: meta.label,
      credits: meta.credit,
      description: item.description,
      icon: <meta.icon className="h-4 w-4" />,
    });
  }
  return result;
}

interface SuggestionsCellProps {
  detail: BusinessItemDetail;
  disabled?: boolean;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
}

export function SuggestionsCell({
  detail,
  disabled,
  onSave,
}: SuggestionsCellProps) {
  const [suggestions, setSuggestions] = useState<ActionSuggestion[]>(() => {
    // Try to get real suggestions first, fallback to mock if none available
    const realSuggestions = buildSuggestionsFromMetadata(detail);
    return realSuggestions.length > 0
      ? realSuggestions
      : buildFallbackSuggestions(detail);
  });
  const [isGenerating, setIsGenerating] = useState(false);

  // New action creation state
  const [newActionOpen, setNewActionOpen] = useState(false);
  const [newAction, setNewAction] = useState<Partial<ActionItem>>({
    type: "manual",
    content: "",
    credit: 0,
  });
  const [selectedCategory, setSelectedCategory] =
    useState<CategoryKey>("manual");

  // Credits spending hook
  const {
    spendCredits,
    canAfford,
    isLoading: isSpendingCredits,
    balance,
  } = useCreditsSpending();

  // Compute average progress from entry actions (0-100). If parsing fails, return 0.
  const totalProgress = (() => {
    try {
      const raw = detail.actions || "";
      const actions = raw.trim() ? JSON.parse(raw) : [];
      if (!Array.isArray(actions) || actions.length === 0) return 0;
      const sum = actions.reduce(
        (acc: number, a: any) => acc + (Number(a?.progress) || 0),
        0
      );
      return sum / actions.length;
    } catch {
      return 0;
    }
  })();
  const shouldShowLightEffect = totalProgress < 33;

  // Update suggestions when detail metadata changes
  useEffect(() => {
    const realSuggestions = buildSuggestionsFromMetadata(detail);
    if (realSuggestions.length > 0) {
      setSuggestions(realSuggestions);
      setIsGenerating(false);
    } else if (suggestions.length === 0) {
      // Only set fallback if no suggestions at all
      setSuggestions(buildFallbackSuggestions(detail));
    }
  }, [detail.id, detail.title, (detail as any)?.metadata?.suggested_actions]);

  // Listen for SSE suggestion events
  useEffect(() => {
    const handleSuggestionsProgress = (event: CustomEvent) => {
      const { entry_id, suggested_actions } = event.detail;
      if (
        String(entry_id) === String(detail.id) &&
        Array.isArray(suggested_actions)
      ) {
        console.log(
          `[SuggestionsCell] Real-time update for entry ${entry_id}:`,
          suggested_actions.length,
          "actions"
        );
        // Update suggestions immediately with streaming data (backend-provided shape supported)
        const streamingSuggestions = suggested_actions
          .slice(0, 3)
          .map((raw: any, index: number) =>
            normalizeBackendSuggestion(raw, index, detail.title)
          );
        setSuggestions(streamingSuggestions);
      }
    };

    const handleSuggestionsCompleted = (event: CustomEvent) => {
      const { topic_id } = event.detail;
      console.log(
        `[SuggestionsCell] Suggestions completed for topic ${topic_id}, entry ${detail.id}`
      );
      setIsGenerating(false);
      // Suggestions will be updated via metadata in the next render cycle
    };

    const handleSuggestionsStarted = () => {
      setIsGenerating(true);
    };

    if (typeof window !== "undefined") {
      window.addEventListener(
        "siift:suggestions-progress",
        handleSuggestionsProgress as EventListener
      );
      window.addEventListener(
        "siift:suggestions-completed",
        handleSuggestionsCompleted as EventListener
      );
      window.addEventListener(
        "siift:suggestions-started",
        handleSuggestionsStarted as EventListener
      );
    }

    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener(
          "siift:suggestions-progress",
          handleSuggestionsProgress as EventListener
        );
        window.removeEventListener(
          "siift:suggestions-completed",
          handleSuggestionsCompleted as EventListener
        );
        window.removeEventListener(
          "siift:suggestions-started",
          handleSuggestionsStarted as EventListener
        );
      }
    };
  }, [detail.id]);

  // Optional: fetch suggestions from a backend endpoint if provided in metadata
  useEffect(() => {
    const metadata: any = (detail as any)?.metadata;
    const suggestionsUrl: string | undefined =
      metadata?.suggestions_url || metadata?.suggestions_endpoint;
    let cancelled = false;

    const fetchSuggestions = async (url: string) => {
      try {
        setIsGenerating(true);
        // Accept absolute URLs or app-relative API routes
        const endpoint = url.startsWith("http") ? url : url;
        const response = await apiClient
          .get<any>(endpoint, { skipAuth: false })
          .catch(() => null);
        if (!response || cancelled) return;

        const rawList = Array.isArray((response as any)?.data?.suggestions)
          ? (response as any).data.suggestions
          : Array.isArray((response as any)?.suggestions)
          ? (response as any).suggestions
          : Array.isArray(response)
          ? (response as any)
          : [];

        if (Array.isArray(rawList) && rawList.length > 0) {
          const normalized = rawList
            .slice(0, 3)
            .map((raw, idx) =>
              normalizeBackendSuggestion(raw, idx, detail.title)
            );
          if (!cancelled) setSuggestions(normalized);
        }
      } catch (_) {
        // no-op; fall back to existing behavior
      } finally {
        if (!cancelled) setIsGenerating(false);
      }
    };

    if (suggestionsUrl) {
      fetchSuggestions(suggestionsUrl);
    }

    return () => {
      cancelled = true;
    };
  }, [
    detail.id,
    (detail as any)?.metadata?.suggestions_url,
    (detail as any)?.metadata?.suggestions_endpoint,
    detail.title,
  ]);

  // Listen for global shuffle request from table header
  useEffect(() => {
    const handler = () => {
      const realSuggestions = buildSuggestionsFromMetadata(detail);
      setSuggestions(
        realSuggestions.length > 0
          ? realSuggestions
          : buildFallbackSuggestions(detail)
      );
    };
    if (typeof window !== "undefined") {
      window.addEventListener("siift:shuffle-suggestions", handler as any);
    }
    return () => {
      if (typeof window !== "undefined") {
        window.removeEventListener("siift:shuffle-suggestions", handler as any);
      }
    };
  }, [detail.id, detail.title]);

  const handleShuffle = () => {
    const realSuggestions = buildSuggestionsFromMetadata(detail);
    setSuggestions(
      realSuggestions.length > 0
        ? realSuggestions
        : buildFallbackSuggestions(detail)
    );
  };

  const handleClickSuggestion = (suggestion: ActionSuggestion) => {
    // Prefill sheet with suggestion values (all actions share the same stored type)
    const typeLabelToKey: Record<string, CategoryKey> = {
      "Web Search": "web",
      "Internal Check": "internal",
      Instructions: "instructions",
      Manual: "manual",
    };
    const cat =
      parseCategoryKey(suggestion.key) ||
      typeLabelToKey[suggestion.type] ||
      "instructions";
    setSelectedCategory(cat);
    setNewAction({
      type: cat,
      content: suggestion.fullContent || suggestion.title, // Use fullContent with actual idea text
      credit: suggestion.credits,
    });
    setNewActionOpen(true);
  };

  const handleNewAction = () => {
    setSelectedCategory("manual");
    setNewAction({ type: "manual", content: "", credit: 0 });
    setNewActionOpen(true);
  };

  const handleCreateAction = async () => {
    if (newAction.content?.trim()) {
      const creditCost =
        typeof newAction.credit === "number" ? newAction.credit : 0;

      // Spend credits if action has a cost
      if (creditCost > 0) {
        const success = await spendCredits(
          creditCost,
          `Action: ${newAction.content.trim()}`
        );
        if (!success) {
          return; // Credit spending failed, don't create action
        }
      }

      const actionItem: ActionItem = {
        id: crypto.randomUUID(),
        type: selectedCategory,
        content: newAction.content.trim(),
        credit: creditCost,
        progress: 0,
        status: "action",
        results: [],
      };

      const currentActions = detail.actions || "";
      let parsedActions: any[] = [];
      try {
        if (currentActions.trim()) {
          parsedActions = JSON.parse(currentActions);
        }
      } catch (e) {
        parsedActions = currentActions
          .split("\n")
          .filter((item) => item.trim())
          .map((item) => ({
            id: crypto.randomUUID(),
            type: "action",
            content: item.trim(),
            credit: 0,
            progress: 0,
            status: "idea",
            results: [],
          }));
      }

      const updatedActions = [...parsedActions, actionItem];
      onSave(detail.id, "actions", JSON.stringify(updatedActions));
      setNewActionOpen(false);
    }
  };

  return (
    <div className={disabled ? "opacity-50 pointer-events-none" : ""}>
      {/* Vertical suggestions list */}
      <div className="space-y-2">
        <button
          type="button"
          onClick={handleShuffle}
          className="group flex items-center cursor-pointer justify-between w-full bg-[var(--background)]/10 border rounded-sm px-2 py-1  border-[var(--siift-light-mid)]/50  hover:bg-[var(--accent)]/10  hover:border-[var(--accent)] transition-colors  "
          aria-label="Shuffle suggestions"
          disabled={isGenerating}
        >
          <span className="group-hover:animate-pulse">Suggestions</span>
          {isGenerating ? (
            <div className="animate-spin rounded-full h-3 w-3 border border-blue-600 border-t-transparent"></div>
          ) : (
            <Shuffle className="h-3 w-3" />
          )}
        </button>
        {suggestions.map((suggestion, index) => (
          <div
            key={suggestion.key + suggestion.title}
            role="button"
            tabIndex={0}
            onClick={() =>
              canAfford(suggestion.credits)
                ? handleClickSuggestion(suggestion)
                : null
            }
            onKeyDown={(e) =>
              e.key === "Enter" &&
              canAfford(suggestion.credits) &&
              handleClickSuggestion(suggestion)
            }
            className={`group relative overflow-hidden hover:bg-[var(--accent)]/10  hover:border-[var(--accent)] rounded border  border-[var(--siift-light-mid)]/50  py-2 transition-colors ${
              canAfford(suggestion.credits)
                ? "bg-[var(--background)] cursor-pointer"
                : "bg-gray-100 dark:bg-gray-800 opacity-50 cursor-not-allowed"
            }`}
          >
            {shouldShowLightEffect && (
              <div
                className="pointer-events-none absolute inset-0 bg-gradient-to-r from-transparent via-[var(--primary)]/20 to-transparent opacity-0 animate-light-sweep"
                style={{
                  animationDelay: `${index * 0.3}s`,
                }}
              />
            )}
            <div className="space-y-1 relative z-10">
              <p
                className={`text-sm px-2 leading-tight text-[var(--foreground)] line-clamp-2 break-words ${
                  shouldShowLightEffect
                    ? "font-medium group-hover:animate-pulse "
                    : ""
                }`}
              >
                {suggestion.title}
              </p>
              <div className="flex items-center justify-between border-t  border-[var(--siift-light-mid)]/50  pt-2 px-2 mt-2 text-xs font-light text-[var(--muted-foreground)] ">
                <span>{suggestion.type}</span>
                <span className="">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div
                        className="flex items-center gap-1"
                        aria-label="Credits"
                      >
                        <CoinIcon className="h-3 w-3" />
                        {suggestion.credits}
                      </div>
                    </TooltipTrigger>
                    <TooltipContent
                      side="right"
                      align="end"
                      className="max-w-xs"
                    >
                      <p className="text-xs">
                        Credits indicate the estimated cost to run this action.
                      </p>
                      <p className="text-xs mt-2">
                        You'll be charged {suggestion.credits} credits for this
                        action.
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </span>
              </div>
            </div>
          </div>
        ))}

        {/* Manual Action - 4th item */}
        <div
          role="button"
          tabIndex={0}
          onClick={handleNewAction}
          onKeyDown={(e) => e.key === "Enter" && handleNewAction()}
          className="group relative overflow-hidden rounded border  border-[var(--siift-light-mid)]/50  bg-[var(--background)] px-2 py-2 hover:bg-[var(--accent)]/10  hover:border-[var(--accent)] cursor-pointer transition-colors"
        >
          {shouldShowLightEffect && (
            <div
              className="pointer-events-none absolute inset-0 bg-gradient-to-r from-transparent via-[var(--primary)]/20 to-transparent opacity-0 animate-light-sweep"
              style={{ animationDelay: `${0.9}s` }}
            />
          )}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-[var(--muted-foreground)] group-hover:animate-pulse">
                create your own
              </span>
              <Plus className="h-4 w-4" />
            </div>
          </div>
        </div>
      </div>

      {/* New Action Creation Sheet */}
      <Sheet open={newActionOpen} onOpenChange={setNewActionOpen}>
        <SheetContent
          side="bottom"
          className="max-w-[50vw] mx-auto rounded-t-xl pb-4"
        >
          <SheetHeader className="text-center">
            <SheetTitle>Create New Action</SheetTitle>
          </SheetHeader>

          <div className="space-y-4 px-4">
            <div className="space-y-2">
              <label className="text-md font-medium mb-2 block">
                Action Type
              </label>
              <Select
                value={selectedCategory}
                onValueChange={(value: any) => {
                  const cat = value as CategoryKey;
                  setSelectedCategory(cat);
                  const meta = getCategoryMeta(cat);
                  setNewAction((prev) => ({ ...prev, credit: meta.credit }));
                }}
              >
                <SelectTrigger className="w-full">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {(
                    [
                      "web",
                      "internal",
                      "instructions",
                      "manual",
                    ] as CategoryKey[]
                  ).map((key) => {
                    const meta = getCategoryMeta(key);
                    const IconComp = meta.icon;
                    return (
                      <SelectItem
                        key={key}
                        value={key}
                        className="flex items-center gap-2"
                      >
                        <IconComp className="h-4 w-4" />
                        {meta.label}
                        <span className="ml-auto text-xs text-[var(--muted-foreground)]">
                          {meta.credit}{" "}
                          {meta.credit === 1 ? "credit" : "credits"}
                        </span>
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-md font-medium mb-2 block">Content</label>
              <Textarea
                placeholder="Describe what needs to be done..."
                value={newAction.content}
                onChange={(e) =>
                  setNewAction((prev) => ({ ...prev, content: e.target.value }))
                }
                className="min-h-[80px]"
              />
            </div>

            {/* Credit cost and affordability warning */}
            {(newAction.credit || 0) > 0 && (
              <div
                className={`p-3 rounded-lg border text-sm ${
                  canAfford(newAction.credit || 0)
                    ? "bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"
                    : "bg-red-50 dark:bg-red-950/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200"
                }`}
              >
                <div className="flex items-center gap-2">
                  <CoinIcon className="h-4 w-4" />
                  <span className="font-medium">
                    Cost: {newAction.credit}{" "}
                    {(newAction.credit || 0) === 1 ? "credit" : "credits"}
                  </span>
                </div>
                {!canAfford(newAction.credit || 0) && (
                  <p className="mt-1 text-xs">
                    Insufficient credits. You need {newAction.credit} but have{" "}
                    {Math.floor(Math.max(0, balance))}.
                  </p>
                )}
              </div>
            )}
          </div>

          <SheetFooter>
            <div className="flex gap-3 w-full justify-end">
              <SidebarButton
                showBorder={true}
                hoverColor="grey"
                hoverScale={true}
                variant="outline"
                onClick={() => setNewActionOpen(false)}
              >
                Cancel
              </SidebarButton>
              <SidebarButton
                onClick={handleCreateAction}
                showBorder={true}
                trailing={<Plus className="h-4 w-4" />}
                hoverColor="grey"
                className="bg-[var(--siift-light-accent)]/50"
                hoverScale={true}
                disabled={
                  isSpendingCredits ||
                  !newAction.content?.trim() ||
                  !canAfford(newAction.credit || 0)
                }
              >
                {isSpendingCredits ? "Processing..." : "Create Action"}
              </SidebarButton>
            </div>
          </SheetFooter>
        </SheetContent>
      </Sheet>
    </div>
  );
}
