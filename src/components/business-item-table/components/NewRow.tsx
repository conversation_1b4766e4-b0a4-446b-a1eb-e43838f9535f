"use client";

import { TableCell, TableRow } from "@/components/ui/table";
import type { BusinessItemDetail } from "@/types/BusinessSection.types";
import { EditableCell } from "../../EditableCell";

export function NewRow({
  newRowData,
  editingCell,
  setEditingCell,
  onSave,
  isItemLocked = false,
}: {
  newRowData: {
    id: string;
    title: string;
    actions: string;
  };
  editingCell: { id: string; field: string } | null;
  setEditingCell: (cell: { id: string; field: string } | null) => void;
  onSave: (id: string, field: keyof BusinessItemDetail, value: string) => void;
  isItemLocked?: boolean;
}) {
  // Check if idea (title) is filled to enable other fields
  const hasIdea = newRowData.title.trim() !== "";

  return (
    <TableRow className="bg-gray-50 dark:bg-gray-900 border-t-2 border-dashed border-b border-[var(--siift-light-mid)]/50">
      <TableCell className="py-3 px-3 border-r border-[var(--siift-light-mid)]/50 w-12">
        <div className="h-4 w-4"></div>
      </TableCell>
      <TableCell className="font-medium py-3 px-3 border-r border-[var(--siift-light-mid)]/50 w-1/3">
        <EditableCell
          id="new-row"
          field="title"
          value=""
          multiline={true}
          className="font-semibold"
          editingCell={editingCell}
          setEditingCell={setEditingCell}
          onSave={onSave}
          newRowData={newRowData}
        />
      </TableCell>
      <TableCell className="py-3 px-3 border-r border-[var(--siift-light-mid)]/50 w-1/4">
        {/* Suggestions column - empty for new rows */}
        <div className="h-4 w-4"></div>
      </TableCell>
      <TableCell
        className={`py-3 px-3 border-r border-[var(--siift-light-mid)]/50 w-5/12 ${
          !hasIdea ? "relative" : ""
        }`}
      >
        <EditableCell
          id="new-row"
          field="actions"
          value=""
          multiline={true}
          editingCell={editingCell}
          setEditingCell={setEditingCell}
          onSave={onSave}
          newRowData={newRowData}
          disabled={isItemLocked}
        />
      </TableCell>
    </TableRow>
  );
}
