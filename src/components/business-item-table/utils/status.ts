"use client";

import type { BusinessItemDetail } from "@/types/BusinessSection.types";

// Get status color helper - matching card styling
export const getStatusColor = (status: string) => {
  switch (status) {
    case "idea":
      return "bg-yellow-50 dark:bg-yellow-500 text-black dark:text-white border border-yellow-300 dark:border-yellow-700";
    case "action":
      return "bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 border border-blue-200 dark:border-blue-700";
    case "confirmed":
      return "bg-green-100 dark:bg-green-900 text-green-900 dark:text-green-200 border border-green-700 dark:border-green-600";
    case "unproven":
      return "bg-red-100 dark:bg-red-900 text-red-900 dark:text-red-200 border border-red-700 dark:border-red-600";
    default:
      return "bg-muted text-muted-foreground";
  }
};

// Helper function to determine status based on content
export const getAutoStatus = (
  title: string,
  actions: string,
  result: string
): BusinessItemDetail["status"] => {
  const hasTitle = title.trim() !== "";
  const hasActions = actions.trim() !== "";
  const hasResult = result.trim() !== "";

  if (hasTitle && !hasActions && !hasResult) {
    return "idea";
  } else if (hasTitle && hasActions && !hasResult) {
    return "action";
  } else if (hasTitle && hasActions && hasResult) {
    return "confirmed"; // Default when all fields are filled
  }
  return "idea"; // Default fallback
};

// Helper function to check if status should be editable
export const isStatusEditable = (
  title: string,
  actions: string,
  result: string
): boolean => {
  const hasTitle = title.trim() !== "";
  const hasActions = actions.trim() !== "";
  const hasResult = result.trim() !== "";

  // Only editable when all three fields are filled (can choose between confirmed/unproven)
  return hasTitle && hasActions && hasResult;
};
