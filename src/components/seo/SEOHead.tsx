import { Metadata } from "next";

export interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: "website" | "article" | "product";
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  siteName?: string;
  locale?: string;
  noIndex?: boolean;
  canonical?: string;
}

const defaultSEO: Required<
  Omit<SEOProps, "publishedTime" | "modifiedTime" | "author" | "canonical">
> = {
  title: "siift – Find your Founder Mode",
  description:
    "Your strategic co‑founder for getting things done. Cut the noise, focus on the highest‑impact moves, and ship with confidence.",
  keywords: [
    "founder tools",
    "startup productivity",
    "strategic planning",
    "execution",
    "founder mode",
    "startup growth",
    "focus tools",
  ],
  image: "/images/og-image.png",
  url: "https://siift.app",
  type: "website",
  siteName: "siift",
  locale: "en_US",
  noIndex: false,
};

export function generateSEOMetadata(seo: SEOProps = {}): Metadata {
  const {
    title = defaultSEO.title,
    description = defaultSEO.description,
    keywords = defaultSEO.keywords,
    image = defaultSEO.image,
    url = defaultSEO.url,
    type = defaultSEO.type,
    publishedTime,
    modifiedTime,
    author,
    siteName = defaultSEO.siteName,
    locale = defaultSEO.locale,
    noIndex = defaultSEO.noIndex,
    canonical,
  } = seo;

  const fullTitle =
    title === defaultSEO.title ? title : `${title} | ${defaultSEO.siteName}`;
  const fullUrl = url.startsWith("http") ? url : `${defaultSEO.url}${url}`;
  const fullImage = image.startsWith("http")
    ? image
    : `${defaultSEO.url}${image}`;

  const metadata: Metadata = {
    title: fullTitle,
    description,
    keywords: keywords.join(", "),
    authors: author ? [{ name: author }] : undefined,
    creator: siteName,
    publisher: siteName,
    robots: noIndex ? "noindex,nofollow" : "index,follow",
    alternates: {
      canonical: canonical || fullUrl,
    },

    // Open Graph
    openGraph: {
      title: fullTitle,
      description,
      url: fullUrl,
      siteName,
      images: [
        {
          url: fullImage,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
      locale,
      type: type as any,
      publishedTime,
      modifiedTime,
    },

    // Twitter
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description,
      images: [fullImage],
      creator: "@siiftapp", // Replace with your Twitter handle
    },

    // Additional meta tags
    other: {
      "theme-color": "#168A00",
      "msapplication-TileColor": "#168A00",
      "apple-mobile-web-app-capable": "yes",
      "apple-mobile-web-app-status-bar-style": "default",
      "format-detection": "telephone=no",
    },
  };

  return metadata;
}

// Structured data helpers
export function generateArticleStructuredData(article: {
  title: string;
  description: string;
  author: string;
  publishedTime: string;
  modifiedTime?: string;
  image: string;
  url: string;
}) {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    headline: article.title,
    description: article.description,
    author: {
      "@type": "Person",
      name: article.author,
    },
    datePublished: article.publishedTime,
    dateModified: article.modifiedTime || article.publishedTime,
    image: article.image,
    url: article.url,
    publisher: {
      "@type": "Organization",
      name: defaultSEO.siteName,
      logo: {
        "@type": "ImageObject",
        url: `${defaultSEO.url}/images/logo.png`,
      },
    },
  };
}

export function generateOrganizationStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: defaultSEO.siteName,
    url: defaultSEO.url,
    logo: `${defaultSEO.url}/images/logo.png`,
    description: defaultSEO.description,
    sameAs: [
      "https://twitter.com/siiftapp", // Replace with your social media
      "https://linkedin.com/company/siift",
    ],
  };
}

export function generateWebsiteStructuredData() {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: defaultSEO.siteName,
    url: defaultSEO.url,
    description: defaultSEO.description,
    potentialAction: {
      "@type": "SearchAction",
      target: `${defaultSEO.url}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string",
    },
  };
}
