"use client";

import { SidebarButton } from "@/components/ui/sidebar-button";
import { ICON_SIZES } from "@/lib/constants";
import { CoinIcon } from "@/components/ui/icons";
import { useAnalytics } from "@/hooks/useAnalytics";
import { useRouter } from "next/navigation";
import { useCredits } from "@/hooks/queries/useCredits";
import { useCreditsStore } from "@/stores/creditsStore";
import { useUpdateUserByClerkId } from "@/hooks/mutations/useUserMutations";
import { useEffect, useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CreditCard, Plus, RefreshCw } from "lucide-react";
import { addCredits } from "@/lib/credits";

interface CreditsButtonProps {
  credits?: number | string;
  onClick?: () => void;
  size?: "sm" | "md" | "lg";
  variant?:
    | "ghost"
    | "outline"
    | "default"
    | "accent"
    | "secondary"
    | "neon-green";
  layout?: "icon-only" | "horizontal" | "vertical";
  showBorder?: boolean;
  trackFrom?: "project-header" | "project-detail-header" | string;
  navigateToBilling?: boolean;
}

export function CreditsButton({
  credits = 500,
  onClick,
  size = "lg",
  variant = "outline",
  layout = "horizontal",
  showBorder = true,
  trackFrom = "project-header",
  navigateToBilling = false,
}: CreditsButtonProps) {
  const { trackClick, trackCustomEvent } = useAnalytics();
  const router = useRouter();
  const [attentionZoom, setAttentionZoom] = useState(false);
  // Subscribe to store balance changes for real-time updates
  const storeBalance = useCreditsStore((s) => s.balance);
  const setBalance = useCreditsStore((s) => s.setBalance);
  const { data: fetchedBalance, refetch } = useCredits();
  const updateUserByClerkId = useUpdateUserByClerkId();

  // One-time attention zoom after 5 seconds
  useEffect(() => {
    const t = setTimeout(() => {
      setAttentionZoom(true);
      const r = setTimeout(() => setAttentionZoom(false), 600);
      return () => clearTimeout(r);
    }, 5000);
    return () => clearTimeout(t);
  }, []);

  const handleClick = () => {
    trackClick("credits-button", trackFrom);
    trackCustomEvent("navigation_clicked", {
      destination: navigateToBilling ? "billing" : "none",
      from_page:
        trackFrom === "project-detail-header"
          ? "item-detail"
          : "project-detail",
      location: "header",
    });
    if (navigateToBilling) {
      router.push("/user-dashboard?tab=billing");
    }
    onClick?.();
  };

  // Always prioritize live balance from store/API over static props
  const effectiveCredits = fetchedBalance ?? storeBalance ?? credits ?? 500;

  const handleIncreaseCredits = async (amount: number = 100) => {
    try {
      // Use the new API function to add credits
      const result = await addCredits(amount);
      const newBalance = result.balance;

      // Update local store
      setBalance(newBalance);

      trackCustomEvent("credits_increased", { amount, from: trackFrom });

      // Background persist to user profile
      try {
        updateUserByClerkId.mutate({ creditBalance: newBalance } as any);
      } catch {}

      // Trigger a background refresh
      refetch();
    } catch (error) {
      console.error("Failed to increase credits:", error);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <SidebarButton
          onClick={handleClick}
          icon={({ className }) => <CoinIcon className={className} />}
          variant={variant}
          size={size}
          showBorder={showBorder}
          layout={layout}
          hoverColor="grey"
          className={`px-4 transition-transform duration-500 ${
            attentionZoom ? "scale-110" : ""
          }`}
          hoverScale={true}
          iconClassName={ICON_SIZES.lg}
          text={`${effectiveCredits}`}
        />
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="w-64 rounded-lg animate-in fade-in-0 zoom-in-95"
        align="end"
        side="bottom"
        sideOffset={4}
      >
        <DropdownMenuLabel className="text-[var(--muted-foreground)] text-sm">
          Credits
        </DropdownMenuLabel>
        <div className="px-2 pb-1 text-sm text-[var(--foreground)] flex items-center justify-between">
          <div>
            Balance:{" "}
            <span className="font-medium">{`${effectiveCredits}`}</span>
          </div>
          <SidebarButton
            variant="outline"
            size="sm"
            icon={({ className }) => <RefreshCw className={className} />}
            onClick={async () => {
              trackCustomEvent("credits_refresh_clicked", { from: trackFrom });
              await refetch();
            }}
          />
        </div>
        <DropdownMenuSeparator />
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
