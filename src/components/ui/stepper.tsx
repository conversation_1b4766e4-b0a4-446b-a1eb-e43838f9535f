"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Logo } from "@/components/ui/logo";
import { Textarea } from "@/components/ui/textarea";
import { AnimatePresence, motion } from "framer-motion";
import { Check, ChevronLeft, ChevronRight } from "lucide-react";
import { useEffect, useState } from "react";
import { DottedBackground } from "./dotted-background";

export interface StepperOption {
  value: string | number | boolean;
  label: string;
}

export interface StepperQuestion {
  id: string;
  title: string;
  subtitle?: string;
  type: "single" | "multiple" | "text" | "textarea";
  options?: StepperOption[];
  placeholder?: string;
  required?: boolean;
}

export interface StepperConfig {
  questions: StepperQuestion[];
  onComplete: (answers: Record<string, any>) => void;
  onStepChange?: (currentStep: number, answers: Record<string, any>) => void;
}

export interface StepperProps {
  config: StepperConfig;
  className?: string;
  isLoading?: boolean;
}

export function Stepper({
  config,
  className = "",
  isLoading = false,
}: StepperProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [canProceed, setCanProceed] = useState(false);

  const currentQuestion = config.questions[currentStep];
  const isLastStep = currentStep === config.questions.length - 1;
  const isFirstStep = currentStep === 0;

  // Check if current question is answered and can proceed
  useEffect(() => {
    if (!currentQuestion) return;

    const currentAnswer = answers[currentQuestion.id];

    if (
      currentQuestion.type === "text" ||
      currentQuestion.type === "textarea"
    ) {
      setCanProceed(!!currentAnswer && currentAnswer.trim().length > 0);
    } else if (currentQuestion.type === "multiple") {
      setCanProceed(Array.isArray(currentAnswer) && currentAnswer.length > 0);
    } else if (currentQuestion.type === "single") {
      setCanProceed(currentAnswer !== undefined && currentAnswer !== null);
    } else {
      setCanProceed(false);
    }
  }, [currentQuestion, answers]);

  // Auto-advance for single choice questions
  useEffect(() => {
    if (currentQuestion?.type === "single" && canProceed && !isLastStep) {
      const timer = setTimeout(() => {
        handleNext();
      }, 800); // Small delay for visual feedback
      return () => clearTimeout(timer);
    }
  }, [canProceed, currentQuestion?.type, isLastStep]);

  const handleNext = () => {
    if (isLastStep) {
      config.onComplete(answers);
    } else {
      const nextStep = currentStep + 1;
      setCurrentStep(nextStep);
      config.onStepChange?.(nextStep, answers);
    }
  };

  const handlePrevious = () => {
    if (!isFirstStep) {
      const prevStep = currentStep - 1;
      setCurrentStep(prevStep);
      config.onStepChange?.(prevStep, answers);
    }
  };

  const handleAnswer = (questionId: string, value: any) => {
    setAnswers((prev) => ({
      ...prev,
      [questionId]: value,
    }));
  };

  const handleSingleChoice = (value: any) => {
    handleAnswer(currentQuestion.id, value);
  };

  const handleMultipleChoice = (value: any, checked: boolean) => {
    const currentAnswers = answers[currentQuestion.id] || [];
    if (checked) {
      handleAnswer(currentQuestion.id, [...currentAnswers, value]);
    } else {
      handleAnswer(
        currentQuestion.id,
        currentAnswers.filter((v: any) => v !== value)
      );
    }
  };

  const handleTextInput = (value: string) => {
    handleAnswer(currentQuestion.id, value);
  };

  if (!currentQuestion) return null;

  return (
    <div
      className={`min-h-screen bg-background relative overflow-hidden ${className}`}
    >
      <div className="absolute inset-0">
        <DottedBackground
          fadeEdge={25}
          dotSizes={[1, 1.5, 2]}
          spacing={25}
          dotsPerRow={8}
          opacity={0.015}
          darkOpacity={0.15}
          lightColors={["CCCCCC", "BBBBBB", "DDDDDD"]}
          darkColors={["666666", "777777", "555555"]}
          className="bg-center"
        />
      </div>

      {/* Noise Texture Overlay */}
      <div
        className="absolute inset-0 opacity-[0.02] dark:opacity-[0.04]"
        style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 256 256' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E")`,
          backgroundSize: "256px 256px",
        }}
      />

      {/* Main Content */}
      <div className="relative z-10 flex flex-col min-h-screen p-4">
        <div className="flex-1 flex items-center justify-center">
          <div className="w-full max-w-md">
            {/* Question Card */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <Card className="border">
                  <CardHeader className="text-center pb-3">
                    <CardTitle className="text-lg font-semibold">
                      {currentQuestion.title}
                    </CardTitle>
                    {currentQuestion.subtitle && (
                      <p className="text-sm text-muted-foreground">
                        {currentQuestion.subtitle}
                      </p>
                    )}
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Single Choice */}
                    {currentQuestion.type === "single" && (
                      <div className="space-y-2">
                        {currentQuestion.options?.map((option) => (
                          <Button
                            key={option.value.toString()}
                            variant={
                              answers[currentQuestion.id] === option.value
                                ? "default"
                                : "outline"
                            }
                            className="w-full justify-start h-auto py-3 px-4 text-left"
                            onClick={() => handleSingleChoice(option.value)}
                          >
                            <div className="flex items-center justify-between w-full">
                              <span>{option.label}</span>
                              {answers[currentQuestion.id] === option.value && (
                                <Check className="w-4 h-4" />
                              )}
                            </div>
                          </Button>
                        ))}
                      </div>
                    )}

                    {/* Multiple Choice */}
                    {currentQuestion.type === "multiple" && (
                      <div className="space-y-2">
                        {currentQuestion.options?.map((option) => {
                          const isChecked = (
                            answers[currentQuestion.id] || []
                          ).includes(option.value);
                          return (
                            <div
                              key={option.value.toString()}
                              className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-muted/50 cursor-pointer"
                              onClick={() =>
                                handleMultipleChoice(option.value, !isChecked)
                              }
                            >
                              <Checkbox
                                checked={isChecked}
                                onCheckedChange={(checked: boolean) =>
                                  handleMultipleChoice(option.value, checked)
                                }
                              />
                              <span className="flex-1">{option.label}</span>
                            </div>
                          );
                        })}
                      </div>
                    )}

                    {/* Text Input */}
                    {currentQuestion.type === "text" && (
                      <Input
                        placeholder={
                          currentQuestion.placeholder || "Enter your answer..."
                        }
                        value={answers[currentQuestion.id] || ""}
                        onChange={(e) => handleTextInput(e.target.value)}
                        className="w-full"
                        autoFocus
                      />
                    )}

                    {/* Textarea Input */}
                    {currentQuestion.type === "textarea" && (
                      <Textarea
                        placeholder={
                          currentQuestion.placeholder || "Enter your answer..."
                        }
                        value={answers[currentQuestion.id] || ""}
                        onChange={(e) => handleTextInput(e.target.value)}
                        className="w-full min-h-[100px]"
                        autoFocus
                      />
                    )}

                    {/* Navigation - Only show for non-single choice or last step */}
                    {(currentQuestion.type !== "single" || isLastStep) && (
                      <div className="flex justify-between pt-4">
                        {!isLastStep && (
                          <Button
                            variant="outline"
                            onClick={handlePrevious}
                            disabled={isFirstStep}
                            className="flex items-center gap-2"
                            size="sm"
                          >
                            <ChevronLeft className="w-4 h-4" />
                            Previous
                          </Button>
                        )}

                        <Button
                          onClick={handleNext}
                          disabled={!canProceed || (isLastStep && isLoading)}
                          className={`flex items-center gap-2 bg-[#166534] hover:bg-[#166534]/90 ${
                            isLastStep ? "w-full text-lg py-4" : "ml-auto"
                          }`}
                          size={isLastStep ? "lg" : "sm"}
                        >
                          {isLastStep && isLoading ? (
                            <>
                              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                              Creating Project...
                            </>
                          ) : isLastStep ? (
                            "Complete"
                          ) : (
                            "Next"
                          )}
                          {!isLastStep && <ChevronRight className="w-4 h-4" />}
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </AnimatePresence>

            {/* Progress Indicator - Below Question */}
            <div className="mt-8 space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-muted-foreground">
                  Step {currentStep + 1} of {config.questions.length}
                </span>
                <span className="text-xs text-muted-foreground">
                  {Math.round(
                    ((currentStep + 1) / config.questions.length) * 100
                  )}
                  %
                </span>
              </div>
              <div className="w-full bg-muted rounded-full h-1">
                <motion.div
                  className="bg-[#166534] h-1 rounded-full"
                  initial={{ width: 0 }}
                  animate={{
                    width: `${
                      ((currentStep + 1) / config.questions.length) * 100
                    }%`,
                  }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            </div>

            {/* Siift Logo - Below Progress */}
            <div className="flex justify-center mt-6">
              <Logo
                size={24}
                textSize={14}
                animated={true}
                showText={true}
                className="opacity-60"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
