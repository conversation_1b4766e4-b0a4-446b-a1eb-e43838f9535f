"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

interface PopoverProps {
  children: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

interface PopoverTriggerProps {
  children: React.ReactNode;
  onClick?: () => void;
}

interface PopoverContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  align?: "start" | "center" | "end";
  side?: "top" | "right" | "bottom" | "left";
}

const PopoverContext = React.createContext<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
}>({
  open: false,
  onOpenChange: () => {},
});

function Popover({
  children,
  open = false,
  onOpenChange = () => {},
}: PopoverProps) {
  return (
    <PopoverContext.Provider value={{ open, onOpenChange }}>
      <div className="relative inline-block">{children}</div>
    </PopoverContext.Provider>
  );
}

function PopoverTrigger({ children, onClick }: PopoverTriggerProps) {
  const { open, onOpenChange } = React.useContext(PopoverContext);

  return (
    <div
      onClick={() => {
        onOpenChange(!open);
        onClick?.();
      }}
      className="cursor-pointer"
    >
      {children}
    </div>
  );
}

function PopoverContent({
  children,
  className,
  align = "center",
  side = "bottom",
  ...props
}: PopoverContentProps) {
  const { open, onOpenChange } = React.useContext(PopoverContext);

  if (!open) return null;

  const alignmentClasses = {
    start: "left-0",
    center: "left-1/2 transform -translate-x-1/2",
    end: "right-0",
  };

  const sideClasses = {
    top: "bottom-full mb-2",
    right: "left-full ml-2 top-0",
    bottom: "top-full mt-2",
    left: "right-full mr-2 top-0",
  };

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 z-40" onClick={() => onOpenChange(false)} />

      {/* Popover */}
      <div
        className={cn(
          "absolute z-50 bg-[var(--background)] border border-[var(--siift-light-mid)]/50  rounded-lg shadow-lg p-4 min-w-[200px] max-w-[300px]",
          sideClasses[side],
          alignmentClasses[align],
          className
        )}
        {...props}
      >
        {children}
      </div>
    </>
  );
}

export { Popover, PopoverTrigger, PopoverContent };
