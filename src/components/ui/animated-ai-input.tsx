"use client";

import { SidebarButton } from "@/components/ui/sidebar-button";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { ArrowRight, StopCircleIcon } from "lucide-react";
import { useCallback, useEffect, useRef, useState } from "react";

interface UseAutoResizeTextareaProps {
  minHeight: number;
  maxHeight?: number;
}

function useAutoResizeTextarea({
  minHeight,
  maxHeight,
}: UseAutoResizeTextareaProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const adjustHeight = useCallback(
    (reset?: boolean) => {
      const textarea = textareaRef.current;
      if (!textarea) return;

      if (reset) {
        textarea.style.height = `${minHeight}px`;
        return;
      }

      textarea.style.height = `${minHeight}px`;

      const newHeight = Math.max(
        minHeight,
        Math.min(textarea.scrollHeight, maxHeight ?? Number.POSITIVE_INFINITY)
      );

      textarea.style.height = `${newHeight}px`;
    },
    [minHeight, maxHeight]
  );

  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = `${minHeight}px`;
    }
  }, [minHeight]);

  useEffect(() => {
    const handleResize = () => adjustHeight();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [adjustHeight]);

  return { textareaRef, adjustHeight };
}

interface AI_PromptProps {
  onSendMessage?: (message: string) => void;
  onStop?: () => void;
  isLoading?: boolean;
  placeholder?: string;
}

export function AI_Prompt({
  onSendMessage,
  onStop,
  isLoading = false,
  placeholder = "What can I do for you?",
}: AI_PromptProps = {}) {
  const [value, setValue] = useState("");
  const { textareaRef, adjustHeight } = useAutoResizeTextarea({
    minHeight: 60,
    maxHeight: 200,
  });
  const [selectedModel, setSelectedModel] = useState("GPT-4-1 Mini");

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey && value.trim() && !isLoading) {
      e.preventDefault();
      const messageToSend = value.trim();
      setValue("");
      adjustHeight(true);
      onSendMessage?.(messageToSend);
    }
  };

  const handleSend = () => {
    if (!value.trim() || isLoading) return;
    const messageToSend = value.trim();
    setValue("");
    adjustHeight(true);
    onSendMessage?.(messageToSend);
  };

  return (
    <div className="relative z-10">
      <div className="relative">
        <div
          className="overflow-y-auto relative"
          style={{ maxHeight: "400px" }}
        >
          <Textarea
            id="ai-input-15"
            value={value}
            placeholder={placeholder}
            className={cn(
              "w-full bg-background hover:bg-background backdrop-blur text-foreground placeholder:text-muted-foreground resize-none transition-all duration-300 hover:shadow-lg relative z-10",
              "border-1 border-[var(--siift-mid-accent)] hover:border-[#166534]/50",
              "focus:ring-[#26F000]/40 focus:border-[#166534] focus:outline-none focus:ring-2",
              "min-h-[120px] text-md px-4 py-3 pr-16 rounded-lg",
              "focus-visible:ring-0 focus-visible:ring-offset-0"
            )}
            ref={textareaRef}
            onKeyDown={handleKeyDown}
            onChange={(e) => {
              setValue(e.target.value);
              adjustHeight();
            }}
          />

          {/* Controls positioned at right bottom of input */}
          <div className="absolute bottom-4 right-3 flex items-center gap-2 z-20">
            {isLoading && (
              <SidebarButton
                type="button"
                icon={StopCircleIcon}
                layout="icon-only"
                size="md"
                variant="neon-green"
                iconClassName="text-black"
                disabled={!value.trim() || isLoading}
                aria-label="Stop message"
                onClick={() => onStop?.()}
                className="shadow-lg"
              />
            )}
            {!isLoading && (
              <SidebarButton
                type="button"
                icon={ArrowRight}
                layout="icon-only"
                size="md"
                variant={value.trim() ? "neon-green" : "ghost"}
                iconClassName="text-black"
                disabled={!value.trim() || isLoading}
                aria-label="Send message"
                onClick={handleSend}
                className={cn(
                  "shadow-lg",
                  value.trim() ? "" : "opacity-50 disabled:cursor-not-allowed"
                )}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
