"use client";

import { ICON_SIZES } from "@/lib/constants";
import { cn } from "@/lib/utils";
import * as React from "react";

interface SidebarButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  // Icon props
  icon?: React.ComponentType<{ className?: string }>;
  iconClassName?: string;

  // Text props
  text?: string;
  subText?: string;
  textClassName?: string;
  subTextClassName?: string;

  // Badge props
  badge?: string | number;
  badgeClassName?: string;

  // Trailing element (like chevron, more icon, etc.)
  trailing?: React.ReactNode;

  // Style variants
  variant?:
    | "default"
    | "ghost"
    | "outline"
    | "accent"
    | "secondary"
    | "neon-green";
  size?: "sm" | "md" | "lg";

  // Border
  showBorder?: boolean;
  borderClassName?: string;

  // Layout
  layout?: "horizontal" | "vertical" | "icon-only";

  // Hover effects
  hoverColor?: "green" | "grey" | "accent";
  hoverScale?: boolean;
}

export const SidebarButton = React.forwardRef<
  HTMLButtonElement,
  SidebarButtonProps
>(
  (
    {
      className,
      icon: Icon,
      iconClassName,
      text,
      subText,
      textClassName,
      subTextClassName,
      badge,
      badgeClassName,
      trailing,
      variant = "default",
      size = "md",
      showBorder = false,
      borderClassName,
      layout = "horizontal",
      hoverColor = "green",
      hoverScale = true,
      children,
      ...props
    },
    ref
  ) => {
    // Base styles
    const baseStyles =
      "relative inline-flex items-center justify-center transition-all duration-200 cursor-pointer";

    // Variant styles
    const variantStyles = {
      default: "bg-background hover:bg-sidebar-accent",
      ghost: "bg-transparent hover:bg-sidebar-accent",
      outline: "bg-background border border-input hover:bg-gray-100",
      accent: "bg-sidebar-accent hover:bg-sidebar-accent/80",
      secondary: "bg-gray-900 text-white hover:bg-gray-800",
      "neon-green":
        "bg-[#CEFFC5] text-black border-[#166534]/20 hover:bg-[#26F000] hover:text-black hover:scale-[1.02] active:scale-[0.98] shadow-lg hover:shadow-xl transition-all duration-300",
    };

    // Size styles
    const sizeStyles = {
      sm: layout === "icon-only" ? "h-6 w-6 p-1" : "h-8 px-2 py-1 text-xs",
      md: layout === "icon-only" ? "h-8 w-8 p-1.5" : "h-10 px-3 py-2 text-sm",
      lg: layout === "icon-only" ? "h-10 w-10 p-2" : "h-12 px-4 py-3 text-base",
    };

    // Hover color styles
    const hoverColorStyles = {
      green: "hover:text-[var(--primary-dark)]",
      grey: "hover:text-muted-foreground hover:bg-muted/50 hover:border-border",
      accent: "hover:text-accent-foreground",
    };

    // Layout styles
    const layoutStyles = {
      horizontal: "flex-row gap-2",
      vertical: "flex-col gap-1",
      "icon-only": "flex-row",
    };

    // Border styles
    const borderStyles = showBorder
      ? `border ${
          borderClassName ||
          " border-[var(--siift-light-mid)]/50 hover:border-[var(--accent)] "
        }`
      : "";

    // Rounded styles
    const roundedStyles = "rounded-lg";

    // Icon size based on button size - using centralized constants
    const iconSizes = {
      sm: ICON_SIZES.sm,
      md: ICON_SIZES.md,
      lg: ICON_SIZES.lg,
    };

    return (
      <button
        ref={ref}
        className={cn(
          baseStyles,
          variantStyles[variant],
          sizeStyles[size],
          hoverColorStyles[hoverColor],
          layoutStyles[layout],
          borderStyles,
          roundedStyles,
          layout === "horizontal" && text ? "justify-start" : "",
          layout === "icon-only" ? "justify-center" : "",
          className
        )}
        {...props}
      >
        {/* Icon */}
        {Icon && (
          <Icon
            className={cn(
              iconClassName || iconSizes[size],
              hoverScale
                ? "transition-transform duration-200 hover:scale-110 hover:text-[var(--accent)] hover:border-[var(--accent)]"
                : ""
            )}
          />
        )}

        {/* Text content */}
        {(text || subText) && layout !== "icon-only" && (
          <div
            className={cn(
              "flex flex-col",
              layout === "horizontal" ? "flex-1 text-left" : "text-center",
              layout === "vertical" ? "items-center" : "items-start"
            )}
          >
            {text && (
              <span
                className={cn(
                  "font-medium truncate",
                  size === "sm"
                    ? "text-xs"
                    : size === "md"
                    ? "text-sm"
                    : "text-base",
                  textClassName
                )}
              >
                {text}
              </span>
            )}
            {subText && (
              <span
                className={cn(
                  "text-muted-foreground truncate",
                  size === "sm" ? "text-xs" : "text-xs",
                  subTextClassName
                )}
              >
                {subText}
              </span>
            )}
          </div>
        )}

        {/* Badge */}
        {badge && (
          <span
            className={cn(
              "bg-primary text-primary-foreground rounded-md flex items-center justify-center absolute -top-1 -right-1 z-10",
              size === "sm"
                ? "text-xs h-4 min-w-4"
                : size === "md"
                ? "text-xs h-4 min-w-4"
                : "text-sm h-5 min-w-5",
              badgeClassName
            )}
          >
            {badge}
          </span>
        )}

        {/* Trailing element */}
        {trailing && layout !== "icon-only" && (
          <div className="ml-auto">{trailing}</div>
        )}

        {/* Children (for custom content) */}
        {children}
      </button>
    );
  }
);

SidebarButton.displayName = "SidebarButton";
