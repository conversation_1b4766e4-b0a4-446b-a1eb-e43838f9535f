"use client";

import React from "react";

type ChildRenderer = (availableSize: number) => React.ReactNode;

export interface RotatingCircleProps {
  size: number;
  backgroundVar?: string; // CSS var token name, e.g., '--brand-light'
  borderColorVar?: string; // CSS var token name, e.g., '--brand-medium'
  borderWidth?: number; // px
  childScale?: number; // 0..1 of parent size
  padding?: number; // px padding between parent and child
  rotateDirection?: "cw" | "ccw" | "none";
  duration?: number; // seconds per rotation
  className?: string;
  style?: React.CSSProperties;
  children?: React.ReactNode | ChildRenderer;
  staticAngle?: number; // degrees when animation is off
}

export const RotatingCircle: React.FC<RotatingCircleProps> = ({
  size,
  backgroundVar = "--brand-light",
  borderColorVar = "--brand-medium",
  borderWidth = 1,
  childScale = 0.6667,
  padding = 1.25,
  rotateDirection = "none",
  duration = 3,
  className = "",
  style,
  children,
  staticAngle = 0,
}) => {
  const childSizeRaw = size * childScale - 2 * (padding + borderWidth);
  const childSize = Math.max(0, childSizeRaw);

  const animationName =
    rotateDirection === "cw"
      ? "rotateCW"
      : rotateDirection === "ccw"
      ? "rotateCCW"
      : "";
  const shouldAnimate = rotateDirection !== "none" && childSize > 0;

  const childContent =
    typeof children === "function"
      ? (children as ChildRenderer)(childSize)
      : children;

  // Compute orbit radius so the child's outer edge touches the parent's inner border
  const parentInnerRadius = (size - 2 * borderWidth) / 2;
  const childRadius = childSize / 2.25;
  const orbitRadius = Math.max(0, parentInnerRadius - childRadius - padding);

  return (
    <div
      className={className}
      style={{
        position: "relative",
        width: size,
        height: size,
        borderRadius: "50%",
        background: `var(${backgroundVar})`,
        border: `${borderWidth}px solid var(${borderColorVar})`,
        ...style,
      }}
    >
      {childSize > 0 && (
        <div
          style={{
            position: "absolute",
            left: "50%",
            top: "50%",
            width: 0,
            height: 0,
            transform: shouldAnimate
              ? "translate(-50%, -50%)"
              : `translate(-50%, -50%) rotate(${staticAngle}deg)`,
            animation: shouldAnimate
              ? `${animationName} ${duration}s linear infinite`
              : "none",
          }}
        >
          <div
            style={{
              position: "absolute",
              left: "50%",
              top: "50%",
              width: childSize,
              height: childSize,
              transform: `translate(-50%, -50%) translateX(${orbitRadius}px)`,
            }}
          >
            {childContent}
          </div>
        </div>
      )}

      {shouldAnimate && (
        <style jsx>{`
          @keyframes rotateCW {
            from {
              transform: translate(-50%, -50%) rotate(0deg);
            }
            to {
              transform: translate(-50%, -50%) rotate(360deg);
            }
          }
          @keyframes rotateCCW {
            from {
              transform: translate(-50%, -50%) rotate(0deg);
            }
            to {
              transform: translate(-50%, -50%) rotate(-360deg);
            }
          }
        `}</style>
      )}
    </div>
  );
};

export default RotatingCircle;
