"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Bell,
  Settings,
  User,
  FolderOpen,
  Clock,
  Plus,
  Edit,
  Trash2,
  Star,
  Calendar,
  FileText,
  Users,
  Activity,
} from "lucide-react";

// Mock data for different tabs
const mockProfileData = {
  name: "Admin User",
  email: "<EMAIL>",
  role: "Administrator",
  joinDate: "January 2024",
  lastLogin: "2 hours ago",
  avatar: "",
  stats: {
    totalLogins: 245,
    projectsManaged: 12,
    usersManaged: 1250,
    systemUptime: "99.9%",
  },
};

const mockNotifications = [
  {
    id: 1,
    title: "System Update Available",
    message: "A new system update is ready to be installed",
    type: "info",
    time: "5 minutes ago",
    read: false,
  },
  {
    id: 2,
    title: "New User Registration",
    message: "<PERSON>e has registered for an account",
    type: "success",
    time: "1 hour ago",
    read: false,
  },
  {
    id: 3,
    title: "Server Maintenance",
    message: "Scheduled maintenance completed successfully",
    type: "success",
    time: "3 hours ago",
    read: true,
  },
  {
    id: 4,
    title: "Security Alert",
    message: "Multiple failed login attempts detected",
    type: "warning",
    time: "1 day ago",
    read: true,
  },
];

const mockProjects = [
  {
    id: 1,
    name: "siift Analytics Platform",
    description: "Main analytics dashboard and reporting system",
    status: "active",
    members: 8,
    progress: 85,
    lastUpdated: "2 hours ago",
    priority: "high",
  },
  {
    id: 2,
    name: "User Management System",
    description: "Complete user authentication and management",
    status: "active",
    members: 5,
    progress: 92,
    lastUpdated: "1 day ago",
    priority: "medium",
  },
  {
    id: 3,
    name: "API Documentation",
    description: "Comprehensive API documentation and guides",
    status: "completed",
    members: 3,
    progress: 100,
    lastUpdated: "3 days ago",
    priority: "low",
  },
  {
    id: 4,
    name: "Mobile App Integration",
    description: "Mobile application backend integration",
    status: "planning",
    members: 0,
    progress: 15,
    lastUpdated: "1 week ago",
    priority: "high",
  },
];

const mockRecentActivity = [
  {
    id: 1,
    action: "User Login",
    user: "<EMAIL>",
    timestamp: "2 minutes ago",
    type: "user",
  },
  {
    id: 2,
    action: "Project Updated",
    user: "<EMAIL>",
    details: "siift Analytics Platform",
    timestamp: "15 minutes ago",
    type: "project",
  },
  {
    id: 3,
    action: "New User Registration",
    user: "<EMAIL>",
    timestamp: "1 hour ago",
    type: "user",
  },
  {
    id: 4,
    action: "System Backup",
    user: "system",
    timestamp: "2 hours ago",
    type: "system",
  },
  {
    id: 5,
    action: "Settings Changed",
    user: "<EMAIL>",
    details: "Security settings updated",
    timestamp: "3 hours ago",
    type: "settings",
  },
];

const mockSettings = {
  general: {
    siteName: "siift Admin Panel",
    timezone: "UTC-8 (Pacific)",
    language: "English",
    theme: "System",
  },
  security: {
    twoFactorAuth: true,
    sessionTimeout: "30 minutes",
    passwordPolicy: "Strong",
    loginAttempts: 5,
  },
  notifications: {
    emailNotifications: true,
    pushNotifications: false,
    weeklyReports: true,
    securityAlerts: true,
  },
};

interface AdminTabbedContentProps {
  activeTab: string;
}

export function AdminTabbedContent({ activeTab }: AdminTabbedContentProps) {
  const getTabContent = () => {
    switch (activeTab) {
      case "profile":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Profile Information
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-start gap-6">
                  <Avatar className="h-20 w-20">
                    <AvatarImage src={mockProfileData.avatar} />
                    <AvatarFallback className="text-lg">
                      {mockProfileData.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-2">
                    <h3 className="text-2xl font-bold">
                      {mockProfileData.name}
                    </h3>
                    <p className="text-muted-foreground">
                      {mockProfileData.email}
                    </p>
                    <Badge variant="secondary">{mockProfileData.role}</Badge>
                    <div className="grid grid-cols-2 gap-4 mt-4">
                      <div>
                        <p className="text-sm text-muted-foreground">Joined</p>
                        <p className="font-medium">
                          {mockProfileData.joinDate}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">
                          Last Login
                        </p>
                        <p className="font-medium">
                          {mockProfileData.lastLogin}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Statistics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">
                      {mockProfileData.stats.totalLogins}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Total Logins
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">
                      {mockProfileData.stats.projectsManaged}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Projects Managed
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">
                      {mockProfileData.stats.usersManaged}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      Users Managed
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-indigo-600">
                      {mockProfileData.stats.systemUptime}
                    </p>
                    <p className="text-sm text-muted-foreground">
                      System Uptime
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "settings":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  General Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">Site Name</p>
                    <p className="font-medium">
                      {mockSettings.general.siteName}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Timezone</p>
                    <p className="font-medium">
                      {mockSettings.general.timezone}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Language</p>
                    <p className="font-medium">
                      {mockSettings.general.language}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Theme</p>
                    <p className="font-medium">{mockSettings.general.theme}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Security Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-muted-foreground">
                      Two-Factor Authentication
                    </p>
                    <Badge
                      variant={
                        mockSettings.security.twoFactorAuth
                          ? "default"
                          : "secondary"
                      }
                    >
                      {mockSettings.security.twoFactorAuth
                        ? "Enabled"
                        : "Disabled"}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      Session Timeout
                    </p>
                    <p className="font-medium">
                      {mockSettings.security.sessionTimeout}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      Password Policy
                    </p>
                    <p className="font-medium">
                      {mockSettings.security.passwordPolicy}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">
                      Max Login Attempts
                    </p>
                    <p className="font-medium">
                      {mockSettings.security.loginAttempts}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "notifications":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Recent Notifications
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockNotifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 rounded-lg border ${
                        notification.read ? "bg-muted/50" : "bg-background"
                      }`}
                    >
                      <div className="flex items-start justify-between">
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            <h4 className="font-medium">
                              {notification.title}
                            </h4>
                            {!notification.read && (
                              <Badge
                                variant="default"
                                className="h-2 w-2 p-0 rounded-full"
                              />
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {notification.message}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {notification.time}
                          </p>
                        </div>
                        <Badge
                          variant={
                            notification.type === "success"
                              ? "default"
                              : notification.type === "warning"
                              ? "destructive"
                              : "secondary"
                          }
                        >
                          {notification.type}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case "projects":
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <Card className="flex-1 mr-4">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FolderOpen className="h-5 w-5" />
                    Projects Overview
                  </CardTitle>
                </CardHeader>
              </Card>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Create New Project
              </Button>
            </div>

            <div className="grid gap-4">
              {mockProjects.map((project) => (
                <Card key={project.id}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <h3 className="font-semibold">{project.name}</h3>
                          <Badge
                            variant={
                              project.status === "active"
                                ? "default"
                                : project.status === "completed"
                                ? "secondary"
                                : "outline"
                            }
                          >
                            {project.status}
                          </Badge>
                          <Badge
                            variant={
                              project.priority === "high"
                                ? "destructive"
                                : project.priority === "medium"
                                ? "default"
                                : "secondary"
                            }
                          >
                            {project.priority} priority
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {project.description}
                        </p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            {project.members} members
                          </span>
                          <span className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            Updated {project.lastUpdated}
                          </span>
                        </div>
                      </div>
                      <div className="text-right space-y-2">
                        <p className="text-2xl font-bold">
                          {project.progress}%
                        </p>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );

      case "recent":
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockRecentActivity.map((activity) => (
                    <div
                      key={activity.id}
                      className="flex items-center gap-4 p-3 rounded-lg border"
                    >
                      <div
                        className={`p-2 rounded-full ${
                          activity.type === "user"
                            ? "bg-blue-100 text-blue-600"
                            : activity.type === "project"
                            ? "bg-green-100 text-green-600"
                            : activity.type === "system"
                            ? "bg-purple-100 text-purple-600"
                            : "bg-indigo-100 text-indigo-600"
                        }`}
                      >
                        {activity.type === "user" ? (
                          <Users className="h-4 w-4" />
                        ) : activity.type === "project" ? (
                          <FolderOpen className="h-4 w-4" />
                        ) : activity.type === "system" ? (
                          <Activity className="h-4 w-4" />
                        ) : (
                          <Settings className="h-4 w-4" />
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium">{activity.action}</p>
                        <p className="text-sm text-muted-foreground">
                          {activity.user}
                          {activity.details && ` - ${activity.details}`}
                        </p>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {activity.timestamp}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      default:
        return (
          <Card>
            <CardContent className="p-6">
              <p className="text-muted-foreground">
                Select a tab to view content
              </p>
            </CardContent>
          </Card>
        );
    }
  };

  return <div className="space-y-6">{getTabContent()}</div>;
}
