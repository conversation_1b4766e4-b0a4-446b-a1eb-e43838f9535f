import { LucideIcon } from "lucide-react";

export interface BusinessItem {
  id: string;
  title: string;
  status: "idea" | "action" | "confirmed" | "unproven";
  actions: number;
  ideas: number;
  results: number;
  icon: LucideIcon;
}

export interface BusinessSection {
  id: string;
  title: string;
  items: BusinessItem[];
}

export interface BusinessSectionsData {
  sections: BusinessSection[];
  isLoading: boolean;
  error: string | null;
}

export interface BusinessSectionStore extends BusinessSectionsData {
  // Actions
  setSections: (sections: BusinessSection[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updateItem: (
    sectionId: string,
    itemId: string,
    updates: Partial<BusinessItem>
  ) => void;
  addItem: (sectionId: string, item: BusinessItem) => void;
  removeItem: (sectionId: string, itemId: string) => void;

  // Topic entries management
  getTopicEntries: (sectionId: string, topicId: string) => BusinessItemDetail[];
  setTopicEntries: (
    sectionId: string,
    topicId: string,
    entries: BusinessItemDetail[]
  ) => void;
  addTopicEntry: (
    sectionId: string,
    topicId: string,
    entry: BusinessItemDetail
  ) => void;
  updateTopicEntry: (
    sectionId: string,
    topicId: string,
    entryId: string,
    updates: Partial<BusinessItemDetail>
  ) => void;
  removeTopicEntry: (
    sectionId: string,
    topicId: string,
    entryId: string
  ) => void;
  clearAllTopicEntries: () => void;
  applyEntryLimitsToAllTopics: () => void;

  // Helpers
  findTopicItemById: (topicId: string) => {
    sectionId: string;
    item: (BusinessItem & { entries?: BusinessItemDetail[] }) | null;
  } | null;
}

// Individual business item detail types
export interface BusinessItemDetail {
  id: string;
  title: string;
  status: "idea" | "action" | "confirmed" | "unproven";
  actions: string;
  result: string;
  description?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface BusinessItemStore {
  selectedItem: BusinessItem | null;
  itemDetails: BusinessItemDetail[];
  isLoading: boolean;
  error: string | null;
  // Actions
  setSelectedItem: (item: BusinessItem | null) => void;
  setItemDetails: (details: BusinessItemDetail[]) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  addItemDetail: (detail: BusinessItemDetail) => void;
  updateItemDetail: (id: string, updates: Partial<BusinessItemDetail>) => void;
  removeItemDetail: (id: string) => void;
}
