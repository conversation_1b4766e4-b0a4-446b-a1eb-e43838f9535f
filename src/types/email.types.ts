// Email service types for mock email functionality

export interface EmailTemplate {
  subject: string;
  body: string;
  type: "verification" | "password-reset" | "welcome";
}

export interface VerificationCode {
  code: string;
  email: string;
  type: "email-verification" | "password-reset";
  expiresAt: number; // Unix timestamp
  attempts: number;
  maxAttempts: number;
  createdAt: number;
}

export interface EmailMessage {
  id: string;
  to: string;
  subject: string;
  body: string;
  type: EmailTemplate["type"];
  sentAt: number;
  verificationCode?: string;
}

export interface EmailServiceResponse {
  success: boolean;
  messageId: string;
  code?: string; // For development/testing purposes
  message: string;
}

export interface EmailVerificationRequest {
  email: string;
  name: string;
  type: "registration" | "email-change";
}

export interface PasswordResetRequest {
  email: string;
  name: string;
}

export interface CodeVerificationRequest {
  email: string;
  code: string;
  type: "email-verification" | "password-reset";
}

export interface EmailServiceConfig {
  codeLength: number;
  codeExpirationMinutes: number;
  maxAttempts: number;
  rateLimitMinutes: number;
  maxEmailsPerHour: number;
}

export interface RateLimitInfo {
  email: string;
  count: number;
  resetAt: number;
}

export interface EmailServiceError extends Error {
  code:
    | "RATE_LIMITED"
    | "CODE_EXPIRED"
    | "CODE_INVALID"
    | "MAX_ATTEMPTS_EXCEEDED"
    | "EMAIL_NOT_FOUND";
  details?: any;
}
