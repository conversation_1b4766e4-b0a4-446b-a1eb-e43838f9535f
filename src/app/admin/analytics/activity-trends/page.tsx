"use client";

// Prevent static generation for this page
export const dynamic = "force-dynamic";

import { AdminLayout } from "@/components/layout/admin-layout";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { adminApi, initializeAdminApi } from "@/lib/admin-api";
import {
  Activity,
  AlertCircle,
  Loader2,
  RefreshCw,
  TrendingUp,
  UserPlus,
  Users,
} from "lucide-react";
import { useEffect, useState } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

export default function ActivityTrendsPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);

    try {
      const token =
        localStorage.getItem("siift_access_token") ||
        sessionStorage.getItem("siift_access_token");

      if (!token) {
        throw new Error("No admin token found. Please login first.");
      }

      initializeAdminApi(token);

      const result = await adminApi.getActivityTrends();
      setData(result);
      setLastUpdated(new Date());
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + "M";
    if (num >= 1000) return (num / 1000).toFixed(1) + "K";
    return num.toString();
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds.toFixed(1)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds.toFixed(0)}s`;
  };

  const summaryMetrics = data?.summary
    ? [
        {
          label: "Total Days",
          value: data.summary.totalDays,
          icon: Activity,
          color: "text-blue-600",
          bgColor: "bg-blue-50",
        },
        {
          label: "Avg Active Users",
          value: formatNumber(data.summary.averageActiveUsers),
          icon: Users,
          color: "text-green-600",
          bgColor: "bg-green-50",
        },
        {
          label: "Total New Users",
          value: formatNumber(data.summary.totalNewUsers),
          icon: UserPlus,
          color: "text-purple-600",
          bgColor: "bg-purple-50",
        },
        {
          label: "Growth Rate",
          value: `${data.summary.growthRate.toFixed(1)}%`,
          icon: TrendingUp,
          color: "text-orange-600",
          bgColor: "bg-orange-50",
        },
      ]
    : [];

  // Prepare chart data
  const chartData =
    data?.data?.map((item: any) => ({
      ...item,
      date: new Date(item.date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
    })) || [];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Activity Trends</h1>
            <p className="text-muted-foreground">
              Visualize user activity patterns and trends over time.
            </p>
          </div>
          <div className="flex items-center space-x-4">
            {lastUpdated && (
              <span className="text-sm text-muted-foreground">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </span>
            )}
            <Button onClick={fetchData} disabled={loading} variant="outline">
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Refresh
            </Button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600" />
                <p className="text-red-800">{error}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Loading State */}
        {loading && !data && (
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        )}

        {/* Summary Metrics */}
        {data && (
          <>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {summaryMetrics.map((metric, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className={`p-3 rounded-full ${metric.bgColor}`}>
                        <metric.icon className={`h-6 w-6 ${metric.color}`} />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {metric.label}
                        </p>
                        <p className="text-3xl font-bold">{metric.value}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Active Users Trend Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Active Users Trend</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip
                        formatter={(value: any, name: string) => [
                          formatNumber(value),
                          name === "activeUsers" ? "Active Users" : "New Users",
                        ]}
                      />
                      <Line
                        type="monotone"
                        dataKey="activeUsers"
                        stroke="#166534"
                        strokeWidth={2}
                        dot={{ fill: "#166534", strokeWidth: 2, r: 4 }}
                      />
                      <Line
                        type="monotone"
                        dataKey="newUsers"
                        stroke="#7c3aed"
                        strokeWidth={2}
                        dot={{ fill: "#7c3aed", strokeWidth: 2, r: 4 }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            {/* Requests and Session Time Chart */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Total Requests</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip
                          formatter={(value: any) => [
                            formatNumber(value),
                            "Requests",
                          ]}
                        />
                        <Bar dataKey="totalRequests" fill="#166534" />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Average Session Time</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-64">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={chartData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip
                          formatter={(value: any) => [
                            formatDuration(value),
                            "Session Time",
                          ]}
                        />
                        <Line
                          type="monotone"
                          dataKey="averageSessionTime"
                          stroke="#ea580c"
                          strokeWidth={2}
                          dot={{ fill: "#ea580c", strokeWidth: 2, r: 4 }}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Data Table */}
            <Card>
              <CardHeader>
                <CardTitle>Detailed Data</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">Date</th>
                        <th className="text-right p-2">Active Users</th>
                        <th className="text-right p-2">New Users</th>
                        <th className="text-right p-2">Total Requests</th>
                        <th className="text-right p-2">Avg Session Time</th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.data
                        ?.slice(0, 10)
                        .map((item: any, index: number) => (
                          <tr
                            key={index}
                            className="border-b hover:bg-muted/50"
                          >
                            <td className="p-2">
                              {new Date(item.date).toLocaleDateString()}
                            </td>
                            <td className="text-right p-2">
                              {formatNumber(item.activeUsers)}
                            </td>
                            <td className="text-right p-2">
                              {formatNumber(item.newUsers)}
                            </td>
                            <td className="text-right p-2">
                              {formatNumber(item.totalRequests)}
                            </td>
                            <td className="text-right p-2">
                              {formatDuration(item.averageSessionTime)}
                            </td>
                          </tr>
                        ))}
                    </tbody>
                  </table>
                </div>
                {data.data?.length > 10 && (
                  <div className="mt-4 text-center">
                    <Badge variant="outline">
                      Showing first 10 of {data.data.length} records
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </AdminLayout>
  );
}
