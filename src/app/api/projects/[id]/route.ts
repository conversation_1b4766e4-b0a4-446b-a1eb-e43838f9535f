// src/app/api/projects/[id]/route.ts
import { auth } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

const BACKEND_URL =
  process.env.NEXT_PUBLIC_ADMIN_API_URL ||
  process.env.NEXT_PUBLIC_API_URL ||
  "http://localhost:3002";

const STRICT_BACKEND = process.env.NEXT_PUBLIC_STRICT_BACKEND !== "false";

async function requireToken() {
  const { getToken } = await auth();
  const token = await getToken();
  if (!token) {
    return {
      token: null as string | null,
      res: NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      ),
    };
  }
  return { token, res: null as Response | null };
}

const backendUrl = (id: string) => `${BACKEND_URL}/api/projects/${id}`;

async function forward<T = unknown>(
  method: "GET" | "PATCH" | "DELETE",
  id: string,
  token: string,
  body?: unknown
) {
  const r = await fetch(backendUrl(id), {
    method,
    headers: {
      Authorization: `Bearer ${token}`,
      ...(body ? { "Content-Type": "application/json" } : {}),
    },
    ...(body ? { body: JSON.stringify(body) } : {}),
  });

  if (r.ok) {
    const data = (await r.json()) as T;
    return NextResponse.json({ success: true, data });
  }
  if (STRICT_BACKEND) {
    return NextResponse.json(
      { success: false, error: "Backend error" },
      { status: r.status || 502 }
    );
  }
  return null;
}

/** GET /api/projects/[id] */
export async function GET(
  _req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { token, res } = await requireToken();
    if (!token) return res!;
    const { id } = await params;

    try {
      const ok = await forward("GET", id, token);
      if (ok) return ok;
    } catch {
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend unavailable" },
          { status: 502 }
        );
      }
    }

    // Fallback when STRICT_BACKEND=false
    return NextResponse.json({
      success: true,
      data: {
        id,
        name: `Project ${id}`,
        description: "Mock project description",
        status: "active",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: "mock-user-id",
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch project",
        details: String((error as Error)?.message ?? error),
      },
      { status: 500 }
    );
  }
}

/** PUT (maps to PATCH on backend) /api/projects/[id] */
export async function PUT(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { token, res } = await requireToken();
    if (!token) return res!;
    const { id } = await params;
    const body = await req.json();

    try {
      const ok = await forward("PATCH", id, token, body);
      if (ok) return ok;
    } catch {
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend unavailable" },
          { status: 502 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: "Backend unavailable" },
      { status: 502 }
    );
  } catch {
    return NextResponse.json(
      { success: false, error: "Failed to update project" },
      { status: 500 }
    );
  }
}

/** PATCH /api/projects/[id] */
export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { token, res } = await requireToken();
    if (!token) return res!;
    const { id } = await params;
    const body = await req.json();

    try {
      const ok = await forward("PATCH", id, token, body);
      if (ok) return ok;
    } catch {
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend unavailable" },
          { status: 502 }
        );
      }
    }

    // Soft fallback when STRICT_BACKEND=false
    return NextResponse.json({
      success: true,
      data: { id, ...body, updatedAt: new Date().toISOString() },
    });
  } catch {
    return NextResponse.json(
      { success: false, error: "Failed to update project" },
      { status: 500 }
    );
  }
}

/** DELETE /api/projects/[id] */
export async function DELETE(
  _req: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { token, res } = await requireToken();
    if (!token) return res!;
    const { id } = await params;

    try {
      const ok = await forward("DELETE", id, token);
      if (ok)
        return NextResponse.json({
          success: true,
          message: "Project deleted successfully",
        });
    } catch {
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend unavailable" },
          { status: 502 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: "Backend unavailable" },
      { status: 502 }
    );
  } catch {
    return NextResponse.json(
      { success: false, error: "Failed to delete project" },
      { status: 500 }
    );
  }
}
