import { auth } from "@clerk/nextjs/server";
import { NextRequest, NextResponse } from "next/server";

const BACKEND_URL =
  process.env.NEXT_PUBLIC_ADMIN_API_URL ||
  process.env.NEXT_PUBLIC_API_URL ||
  "http://localhost:3002";
const STRICT_BACKEND = process.env.NEXT_PUBLIC_STRICT_BACKEND !== "false";

// No mock fallback data allowed

export async function GET(request: NextRequest) {
  try {
    const { getToken } = await auth();
    const token = await getToken();

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: "Unauthorized",
        },
        { status: 401 }
      );
    }

    // Try to fetch from real backend
    console.log("🔗 Attempting to fetch projects from backend:", BACKEND_URL);
    console.log(
      "🔑 Using Clerk JWT:",
      token ? `${token.substring(0, 12)}...` : "none"
    );

    try {
      const response = await fetch(`${BACKEND_URL}/api/projects`, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      console.log("📡 Backend response status:", response.status);

      if (response.ok) {
        const raw = await response.json();
        const projects = Array.isArray(raw)
          ? raw
          : Array.isArray(raw?.data)
          ? raw.data
          : Array.isArray(raw?.items)
          ? raw.items
          : Array.isArray(raw?.projects)
          ? raw.projects
          : [];
        console.log(
          "✅ Successfully fetched projects from backend:",
          Array.isArray(projects) ? projects.length : 0,
          "projects"
        );
        return NextResponse.json({
          success: true,
          data: projects,
        });
      } else {
        console.warn(
          "❌ Backend returned error status:",
          response.status,
          response.statusText
        );
        if (STRICT_BACKEND) {
          return NextResponse.json(
            { success: false, error: "Backend error" },
            { status: response.status || 502 }
          );
        }
      }
    } catch (backendError: any) {
      console.warn(
        "🚫 Backend unavailable:",
        backendError?.message || backendError
      );
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend unavailable" },
          { status: 502 }
        );
      }
    }

    // Fallback: return mock projects when backend is unavailable
    console.log("🔄 Using fallback mock projects data");
    return NextResponse.json({
      success: true,
      data: [],
    });
  } catch (error) {
    console.error("Error fetching projects:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch projects",
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { getToken } = await auth();
    const token = await getToken();

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: "Unauthorized",
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, description } = body;

    // Validate required fields
    if (!name || !description) {
      return NextResponse.json(
        {
          success: false,
          error: "Name and description are required",
        },
        { status: 400 }
      );
    }

    // Try to create project in real backend
    try {
      const response = await fetch(`${BACKEND_URL}/api/projects`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name, description }),
      });

      if (response.ok) {
        const project = await response.json();
        return NextResponse.json({
          success: true,
          data: project,
        });
      } else if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend error" },
          { status: response.status || 502 }
        );
      }
    } catch (backendError) {
      console.warn("Backend unavailable:", backendError);
      if (STRICT_BACKEND) {
        return NextResponse.json(
          { success: false, error: "Backend unavailable" },
          { status: 502 }
        );
      }
    }

    // Fallback: return mock project when backend is unavailable
    const mockProject = {
      id: `mock-${Date.now()}`,
      name,
      description,
      status: "active" as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      userId: "mock-user-id",
    };

    console.log("🔄 Using fallback mock project creation:", mockProject.id);
    return NextResponse.json({
      success: true,
      data: mockProject,
    });
  } catch (error) {
    console.error("Error creating project:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to create project",
      },
      { status: 500 }
    );
  }
}
