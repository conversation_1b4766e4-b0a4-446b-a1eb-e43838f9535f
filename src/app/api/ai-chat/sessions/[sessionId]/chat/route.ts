import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';

export async function POST(request: NextRequest, { params }: { params: Promise<{ sessionId: string }> }) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });

    const { sessionId } = await params;
    const body = await request.json().catch(() => ({}));

    console.log('[AI Chat] Sending message to session:', sessionId, { messageLength: body?.message?.length });
    console.log('[AI Chat] Backend URL:', BACKEND_URL);
    console.log('[AI Chat] Request body:', body);

    const payload = (() => {
      const content = body?.content || body?.message || '';
      // Map to new API shape: stage + optional topic_name
      // Backward compat: accept agent_stage / stage in any case style
      const stage = (body?.stage || body?.agent_stage || 'coaching').toString().toLowerCase();
      const normalizedStage = stage === 'COACHING' ? 'coaching' : stage;
      const topic_name = body?.topic_name || body?.topic || body?.topicName || undefined;
      const base: Record<string, unknown> = { content, stage: normalizedStage };
      if (topic_name && typeof topic_name === 'string' && topic_name.trim().length > 0) {
        base.topic_name = topic_name;
      }
      return base;
    })();


    const resp = await fetch(`${BACKEND_URL}/api/ai-chat/sessions/${sessionId}/chat`, {
      method: 'POST',
      headers: { Authorization: `Bearer ${token}`, 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });

    // If the response is a streaming response, forward it
    if (resp.ok && resp.headers.get('content-type')?.includes('text/event-stream')) {
      console.log('[AI Chat] Forwarding streaming response');
      return new Response(resp.body, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
        },
      });
    }

    // Otherwise return JSON response
    console.log('[AI Chat] Backend response status:', resp.status, resp.statusText);
    const data = await resp.json().catch(() => ({}));
    console.log('[AI Chat] Backend response data:', data);
    return NextResponse.json(data, { status: resp.status });
  } catch (e) {
    console.error('[AI Chat] Error:', e);
    return NextResponse.json({ success: false, error: 'Failed to send chat message' }, { status: 500 });
  }
}


