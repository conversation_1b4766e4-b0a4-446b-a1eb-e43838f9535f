import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ projectId: string }> }
) {
  try {
    const { getToken } = await auth();
    const token = await getToken();
    if (!token) return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });

    const { projectId } = await params;
    console.log('[AI Project Sessions] Getting sessions for project:', projectId);
    console.log('[AI Project Sessions] Backend URL:', BACKEND_URL);
    console.log('[AI Project Sessions] Request URL:', `${BACKEND_URL}/api/ai-chat/projects/${projectId}/sessions`);

    const resp = await fetch(`${BACKEND_URL}/api/ai-chat/projects/${projectId}/sessions`, {
      method: 'GET',
      headers: { 
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
    });

    console.log('[AI Project Sessions] Backend response status:', resp.status, resp.statusText);
    console.log('[AI Project Sessions] Backend response headers:', Object.fromEntries(resp.headers.entries()));
    
    if (resp.ok) {
      const data = await resp.json();
      console.log('[AI Project Sessions] Backend response data (FULL):', JSON.stringify(data, null, 2));
      console.log('[AI Project Sessions] Data type:', typeof data);
      console.log('[AI Project Sessions] Is array?:', Array.isArray(data));
      if (Array.isArray(data)) {
        console.log('[AI Project Sessions] Session count:', data.length);
        data.forEach((session, i) => {
          console.log(`[AI Project Sessions] Session ${i}:`, {
            id: session.id,
            session_id: session.session_id,
            sessionId: session.sessionId,
            status: session.status,
            project_id: session.project_id,
            projectId: session.projectId,
            created_at: session.created_at,
            updated_at: session.updated_at
          });
        });
      }
      return NextResponse.json(data);
    } else {
      const errorText = await resp.text();
      console.error('[AI Project Sessions] Backend error:', errorText);
      return NextResponse.json({ 
        success: false, 
        error: `Backend error: ${resp.status} ${resp.statusText}` 
      }, { status: resp.status });
    }
  } catch (e) {
    console.error('[AI Project Sessions] Error:', e);
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to get project sessions' 
    }, { status: 500 });
  }
}
