import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

const BACKEND_URL = process.env.NEXT_PUBLIC_ADMIN_API_URL || process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';
const STRICT_BACKEND = process.env.NEXT_PUBLIC_STRICT_BACKEND !== 'false';

export async function POST(request: NextRequest) {
  try {
    const { getToken } = await auth();
    const token = await getToken();

    if (!token) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Unauthorized' 
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { message, content, projectId, sessionId, agent_stage, stage, topic_name, topicName, topic } = body;

    const text = (content || message || '').toString();
    const incomingStage = (stage || agent_stage || 'coaching').toString().toLowerCase();
    const normalizedStage = incomingStage === 'coaching' ? 'coaching' : incomingStage;
    const normalizedTopic = (topic_name || topicName || topic || '').toString();
    console.log('[Chat API] Received request:', { projectId, sessionId, messageLength: text?.length, stage: normalizedStage, topic_name: normalizedTopic });

    // Validate required fields
    if (!text || !projectId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Message/content and projectId are required' 
        },
        { status: 400 }
      );
    }

    let chatSessionId = sessionId;
    
    // If no session ID provided, create a new AI session
    if (!chatSessionId) {
      console.log('[Chat API] Creating new AI session for project:', projectId);
      try {
        const sessionResponse = await fetch(`${BACKEND_URL}/api/ai-chat/sessions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
          },
          body: JSON.stringify({ projectId }),
        });
        
        if (sessionResponse.ok) {
          const sessionData = await sessionResponse.json();
          chatSessionId = sessionData.sessionId || sessionData.id || sessionData.data?.id;
          console.log('[Chat API] Created session:', chatSessionId);
        } else {
          console.error('[Chat API] Failed to start chat session:', sessionResponse.status, sessionResponse.statusText);
          return NextResponse.json({ error: 'Failed to start chat session' }, { status: sessionResponse.status || 502 });
        }
      } catch (error) {
        console.error('[Chat API] Error starting chat session:', error);
        return NextResponse.json({ error: 'AI backend unavailable' }, { status: 502 });
      }
    }

    if (!chatSessionId) {
      return NextResponse.json({ error: 'No valid session ID available' }, { status: 500 });
    }

    // Send message to AI chat session using correct OpenAPI endpoint
    console.log('[Chat API] Sending message to session:', chatSessionId);
    try {
      const chatResponse = await fetch(`${BACKEND_URL}/api/ai-chat/sessions/${chatSessionId}/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ content: text, stage: normalizedStage, ...(normalizedTopic ? { topic_name: normalizedTopic } : {}) }),
      });

      console.log('[Chat API] Backend response status:', chatResponse.status);

      if (chatResponse.ok && chatResponse.body) {
        // Forward the streaming response from backend
        return new Response(chatResponse.body, {
          headers: {
            'Content-Type': 'text/event-stream',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
          },
        });
      } else {
        console.error('[Chat API] Backend chat error:', chatResponse.status, chatResponse.statusText);
        return NextResponse.json(
          { success: false, error: 'Failed to send message to AI service' },
          { status: chatResponse.status || 502 }
        );
      }
    } catch (backendError) {
      console.error('[Chat API] Backend chat unavailable:', backendError);
      return NextResponse.json(
        { success: false, error: 'Backend unavailable' },
        { status: 502 }
      );
    }


    // Fallback to mock response if backend is unavailable
    const encoder = new TextEncoder();
    
    const stream = new ReadableStream({
      start(controller) {
        const responses = [
          "I understand you're working on ",
          "this project. ",
          "Let me help you with that. ",
          "Here are some suggestions based on your input:\n\n",
          "1. **First consideration**: This looks like an interesting challenge.\n",
          "2. **Next steps**: I'd recommend breaking this down into smaller tasks.\n",
          "3. **Resources**: You might want to research similar approaches.\n\n",
          "Would you like me to elaborate on any of these points?"
        ];

        let index = 0;
        const interval = setInterval(() => {
          if (index < responses.length) {
            const chunk = responses[index];
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({ 
              type: 'content', 
              content: chunk,
              projectId,
              sessionId: chatSessionId 
            })}\n\n`));
            index++;
          } else {
            controller.enqueue(encoder.encode(`data: ${JSON.stringify({ 
              type: 'done',
              projectId,
              sessionId: chatSessionId 
            })}\n\n`));
            controller.close();
            clearInterval(interval);
          }
        }, 150);
      },
    });

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    });
  } catch (error) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to process chat message' 
      },
      { status: 500 }
    );
  }
}