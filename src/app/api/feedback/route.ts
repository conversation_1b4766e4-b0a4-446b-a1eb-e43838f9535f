import { NextRequest, NextResponse } from "next/server";
import { Resend } from "resend";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      projectId,
      type,
      message,
      testimonial,
      displayName,
      email,
      company,
      allowPublic,
      source,
    } = body || {};

    if (!message) {
      return NextResponse.json(
        { success: false, error: "Message is required" },
        { status: 400 }
      );
    }

    // Send an email notification via Resend only (no backend proxy)
    try {
      const apiKey = process.env.RESEND_API_KEY;
      const fromEmail = process.env.RESEND_FROM_EMAIL;

      // Debug logging
      console.log("[Feedback API] Environment check:", {
        hasApiKey: !!apiKey,
        hasFromEmail: !!fromEmail,
        apiKeyLength: apiKey?.length,
        fromEmail,
      });

      if (apiKey && fromEmail) {
        const resend = new Resend(apiKey);
        const subject = `New Feedback: ${String(
          type || "unknown"
        ).toUpperCase()}${projectId ? ` (Project: ${projectId})` : ""}`;
        const lines = [
          `Project ID: ${projectId || "-"}`,
          `Type: ${type || "-"}`,
          `Message: ${message || "-"}`,
          `Is Review/Testimonial: ${!!testimonial}`,
          `Display Name: ${displayName || "-"}`,
          `Email: ${email || "-"}`,
          `Company: ${company || "-"}`,
          `Allow Public: ${!!allowPublic}`,
          `Source: ${source || "project"}`,
        ];
        const text = lines.join("\n");
        const html = `
          <div>
            <h2 style="margin:0 0 12px 0;">New Feedback Received</h2>
            <ul style="padding-left:16px;">
              <li><strong>Project ID:</strong> ${projectId || "-"}</li>
              <li><strong>Type:</strong> ${type || "-"}</li>
              <li><strong>Is Review/Testimonial:</strong> ${!!testimonial}</li>
              <li><strong>Display Name:</strong> ${displayName || "-"}</li>
              <li><strong>Email:</strong> ${email || "-"}</li>
              <li><strong>Company:</strong> ${company || "-"}</li>
              <li><strong>Allow Public:</strong> ${!!allowPublic}</li>
              <li><strong>Source:</strong> ${source || "project"}</li>
            </ul>
            <div>
              <strong>Message:</strong>
              <pre style="white-space:pre-wrap;border:1px solid #eee;padding:12px;border-radius:6px;">${
                message || "-"
              }</pre>
            </div>
          </div>
        `;

        console.log("[Feedback API] Attempting to send email with:", {
          from: fromEmail,
          to: "<EMAIL>, <EMAIL>",
          subject,
        });

        const emailResult = await resend.emails.send({
          from: fromEmail,
          to: ["<EMAIL>", "<EMAIL>"],
          subject,
          text,
          html,
        });

        console.log("[Feedback API] Email result:", emailResult);

        if (!emailResult || (emailResult as any)?.error) {
          throw new Error(
            `Failed to send email via Resend: ${JSON.stringify(emailResult)}`
          );
        }
      } else {
        console.error("[Feedback API] Missing environment variables:", {
          apiKey: !!apiKey,
          fromEmail: !!fromEmail,
        });
        return NextResponse.json(
          { success: false, error: "Email service not configured" },
          { status: 500 }
        );
      }
    } catch (emailError) {
      // eslint-disable-next-line no-console
      console.error(
        "[Feedback API] Failed to send email via Resend:",
        emailError
      );
      return NextResponse.json(
        {
          success: false,
          error: `Failed to send feedback email: ${emailError}`,
        },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (e: any) {
    console.error("[Feedback API] Unexpected error:", e);
    return NextResponse.json(
      { success: false, error: e?.message || "Unexpected error" },
      { status: 500 }
    );
  }
}
