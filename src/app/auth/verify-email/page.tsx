"use client";

// Prevent static generation for this page
export const dynamic = "force-dynamic";

import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeft, Loader2, Mail, Shield } from "lucide-react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { AuthCard } from "@/components/auth/auth-card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useAuth } from "@/hooks/useAuth";
import { showErrorToast, showSuccessToast } from "@/hooks/useToast";

const verifyEmailSchema = z.object({
  code: z
    .string()
    .min(6, "Verification code must be 6 digits")
    .max(6, "Verification code must be 6 digits"),
});

type VerifyEmailFormData = z.infer<typeof verifyEmailSchema>;

function VerifyEmailForm() {
  const [codeDigits, setCodeDigits] = useState<string[]>([
    "",
    "",
    "",
    "",
    "",
    "",
  ]);
  const [success, setSuccess] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const [isVerifying, setIsVerifying] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const emailFromQuery = searchParams?.get?.("email") || "";
  const {
    verifyEmail,
    resendEmailVerification,
    isLoading,
    clearError,
    emailVerificationSent,
  } = useAuth();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    reset,
  } = useForm<VerifyEmailFormData>({
    resolver: zodResolver(verifyEmailSchema),
    defaultValues: {
      code: "",
    },
  });

  // Clear form and code digits on component mount
  useEffect(() => {
    reset();
    setCodeDigits(["", "", "", "", "", ""]);
  }, [reset]);

  // Handle resend cooldown
  useEffect(() => {
    if (resendCooldown > 0) {
      const timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [resendCooldown]);

  const onSubmit = async (data: VerifyEmailFormData) => {
    clearError();
    setIsVerifying(true);

    try {
      await verifyEmail(emailFromQuery, data.code);
      setSuccess(true);

      showSuccessToast("Email verified successfully!", {
        description: "Redirecting to dashboard...",
      });

      // Redirect to dashboard after success
      setTimeout(() => {
        router.push("/dashboard");
      }, 2000);
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Verification failed";
      showErrorToast("Verification failed", {
        description: errorMessage,
      });
      console.error("Email verification failed:", error);
    } finally {
      setIsVerifying(false);
    }
  };

  const handleResendCode = async () => {
    if (resendCooldown > 0 || !emailFromQuery) return;

    clearError();
    setResendCooldown(60); // 60 second cooldown

    try {
      await resendEmailVerification(emailFromQuery);
      showSuccessToast("Verification code sent!", {
        description: "Check your email for the new code",
      });
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to resend code";
      showErrorToast("Failed to resend code", {
        description: errorMessage,
      });
      console.error("Failed to resend verification code:", error);
      setResendCooldown(0); // Reset cooldown on error
    }
  };

  if (success) {
    return (
      <AuthCard
        title="Email verified successfully!"
        header={
          <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center">
            <Shield className="h-6 w-6 text-green-600 dark:text-green-400" />
          </div>
        }
      >
        <div className="text-center space-y-4">
          <div className="space-y-2">
            <p className="text-sm text-muted-foreground">
              Your email has been verified. You now have full access to your
              account.
            </p>
          </div>
          <p className="text-xs text-muted-foreground">
            Redirecting to dashboard...
          </p>
          <Link
            href="/dashboard"
            className="font-medium text-primary hover:underline"
          >
            Continue to dashboard
          </Link>
        </div>
      </AuthCard>
    );
  }

  return (
    <AuthCard
      title="Verify your email"
      description={`Enter the verification code sent to ${emailFromQuery}`}
      header={
        <div className="mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
          <Mail className="h-6 w-6 text-blue-600 dark:text-blue-400" />
        </div>
      }
      footer={
        <div className="text-center space-y-2">
          <p className="text-sm text-muted-foreground text-center">
            Didn't receive the code?{" "}
            <Button
              variant="link"
              className="px-0 font-normal"
              onClick={handleResendCode}
              disabled={isLoading || resendCooldown > 0 || !emailFromQuery}
            >
              {resendCooldown > 0
                ? `Resend in ${resendCooldown}s`
                : "Resend code"}
            </Button>
          </p>
          <div className="flex w-full">
            <Link
              href="/auth/login"
              className="flex items-center text-sm font-medium text-primary hover:underline w-full pl-0"
              style={{ marginLeft: 0 }}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to login
            </Link>
          </div>
        </div>
      }
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <p className="text-sm text-muted-foreground text-center">
            Enter the 6-digit code sent to your email
          </p>
          <div className="flex justify-center gap-2">
            {Array.from({ length: 6 }).map((_, index) => (
              <Input
                key={index}
                type="text"
                inputMode="numeric"
                maxLength={1}
                value={codeDigits[index]}
                autoComplete="off"
                autoCorrect="off"
                autoCapitalize="off"
                spellCheck={false}
                className={`w-12 h-12 text-center text-lg font-semibold ${
                  errors.code ? "border-destructive" : ""
                }`}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, "");

                  // Update the digits array
                  const newDigits = [...codeDigits];
                  newDigits[index] = value;
                  setCodeDigits(newDigits);

                  // Update form value
                  const newCode = newDigits.join("");
                  setValue("code", newCode);

                  // Auto-focus next input
                  if (index < 5 && value) {
                    const nextInput = (e.target as HTMLInputElement)
                      .parentElement?.children[index + 1] as HTMLInputElement;
                    nextInput?.focus();
                  }
                }}
                onKeyDown={(e) => {
                  if (e.key === "Backspace") {
                    if (!e.currentTarget.value && index > 0) {
                      // Move to previous input if current is empty
                      const prevInput = (e.target as HTMLInputElement)
                        .parentElement?.children[index - 1] as HTMLInputElement;
                      prevInput?.focus();
                    } else if (e.currentTarget.value) {
                      // Clear current input
                      const newDigits = [...codeDigits];
                      newDigits[index] = "";
                      setCodeDigits(newDigits);
                      setValue("code", newDigits.join(""));
                    }
                  }
                }}
                onPaste={(e) => {
                  e.preventDefault();
                  const pastedData = e.clipboardData
                    .getData("text")
                    .replace(/\D/g, "");
                  if (pastedData.length <= 6) {
                    const newDigits = pastedData
                      .split("")
                      .concat(Array(6).fill(""))
                      .slice(0, 6);
                    setCodeDigits(newDigits);
                    setValue("code", pastedData);

                    // Focus the next empty input or the last input
                    const nextEmptyIndex = Math.min(pastedData.length, 5);
                    const nextInput = (e.target as HTMLInputElement)
                      .parentElement?.children[
                      nextEmptyIndex
                    ] as HTMLInputElement;
                    nextInput?.focus();
                  }
                }}
              />
            ))}
          </div>
          <input type="hidden" {...register("code")} />
          {errors.code && (
            <p className="text-sm text-destructive text-center">
              {errors.code.message}
            </p>
          )}
        </div>

        <Button type="submit" className="w-full" disabled={isVerifying}>
          {isVerifying ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Verifying...
            </>
          ) : (
            "Verify Email"
          )}
        </Button>
      </form>
    </AuthCard>
  );
}

export default function VerifyEmailPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <VerifyEmailForm />
    </Suspense>
  );
}
