"use client";

// Prevent static generation for this page
export const dynamic = "force-dynamic";

import { zodResolver } from "@hookform/resolvers/zod";
import { Calendar, Loader2, Mail, Save, Shield } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { DashboardLayout } from "@/components/layout/dashboard-layout";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { useClerkAuth } from "@/hooks/useClerkAuth";
import { useUser } from "@/hooks/queries/useUser";
import { useUpdateUserByClerkId } from "@/hooks/mutations/useUserMutations";

const profileSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  bio: z.string().max(500, "Bio must be less than 500 characters").optional(),
  location: z
    .string()
    .max(100, "Location must be less than 100 characters")
    .optional(),
  website: z
    .string()
    .url("Please enter a valid URL")
    .optional()
    .or(z.literal("")),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export default function ProfilePage() {
  const { user, firstName, lastName, email, imageUrl, fullName } =
    useClerkAuth();
  const { data: backendUser, isLoading: isUserLoading } = useUser();
  const updateUser = useUpdateUserByClerkId();
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  // Mock user metadata
  const [userMetadata, setUserMetadata] = useState({
    bio: "Software developer passionate about building great products",
    location: "San Francisco, CA",
    website: "https://example.com",
  });

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    setValue,
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      firstName: firstName || "",
      lastName: lastName || "",
      bio: userMetadata.bio || "",
      location: userMetadata.location || "",
      website: userMetadata.website || "",
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    if (!user) return;

    setIsLoading(true);
    setMessage(null);

    try {
      // Update user in backend using TanStack Query mutation
      await updateUser.mutateAsync({
        firstName: data.firstName,
        lastName: data.lastName,
        bio: data.bio,
        timezone: "UTC", // You can make this configurable
        preferences: {
          ...userMetadata,
          location: data.location,
          website: data.website,
        },
      });

      // Update the local user metadata
      setUserMetadata({
        bio: data.bio || "",
        location: data.location || "",
        website: data.website || "",
      });

      setMessage({ type: "success", text: "Profile updated successfully!" });
      reset(data); // Reset form to mark as not dirty
    } catch (error: any) {
      setMessage({
        type: "error",
        text: error.message || "Failed to update profile",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (!user) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-destructive mb-2">
              Access Denied
            </h1>
            <p className="text-muted-foreground">
              You need to be signed in to view this page.
            </p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const initials = `${firstName?.[0] || ""}${
    lastName?.[0] || ""
  }`.toUpperCase();

  return (
    <DashboardLayout>
      <div className="space-y-6 px-3">
        {/* Header */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground">
            Profile
          </h1>
          <p className="text-muted-foreground">
            Manage your account settings and profile information.
          </p>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* Profile Card */}
          <Card className="lg:col-span-1 bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
            <CardHeader>
              <CardTitle className="text-card-foreground">
                Profile Information
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                Your personal information and account details.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-4">
                <Avatar className="h-16 w-16 border-2 border-accent/20">
                  <AvatarImage src={imageUrl || ""} alt={fullName || "User"} />
                  <AvatarFallback className="text-lg bg-accent text-accent-foreground font-semibold">
                    {firstName?.[0]}
                    {lastName?.[0]}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold text-card-foreground">
                    {fullName || "User"}
                  </h3>
                  <p className="text-sm text-muted-foreground">{email}</p>
                  <Badge
                    variant="secondary"
                    className="mt-1 bg-accent/10 text-accent border-accent/20"
                  >
                    User
                  </Badge>
                </div>
              </div>

              <Separator className="bg-border" />

              <div className="space-y-3">
                <div className="flex items-center space-x-2 text-sm">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">Email:</span>
                  <span className="text-card-foreground">{email}</span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">Joined:</span>
                  <span className="text-card-foreground">
                    {user?.createdAt
                      ? new Date(user.createdAt).toLocaleDateString()
                      : "Recently"}
                  </span>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <Shield className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">Status:</span>
                  <Badge
                    variant="outline"
                    className="text-xs border-green-500/20 text-green-600 bg-green-500/10"
                  >
                    Active
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Edit Profile Form */}
          <Card className="lg:col-span-2 bg-gray-50 dark:bg-card border hover:shadow-md transition-all duration-200">
            <CardHeader>
              <CardTitle className="text-card-foreground">
                Edit Profile
              </CardTitle>
              <CardDescription className="text-muted-foreground">
                Update your personal information and preferences.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="firstName" className="text-card-foreground">
                      First Name
                    </Label>
                    <Input
                      id="firstName"
                      {...register("firstName")}
                      placeholder="Enter your first name"
                      className="border-border focus:border-accent focus:ring-accent/20"
                    />
                    {errors.firstName && (
                      <p className="text-sm text-destructive">
                        {errors.firstName.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="lastName" className="text-card-foreground">
                      Last Name
                    </Label>
                    <Input
                      id="lastName"
                      {...register("lastName")}
                      placeholder="Enter your last name"
                      className="border-border focus:border-accent focus:ring-accent/20"
                    />
                    {errors.lastName && (
                      <p className="text-sm text-destructive">
                        {errors.lastName.message}
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bio" className="text-card-foreground">
                    Bio
                  </Label>
                  <Textarea
                    id="bio"
                    {...register("bio")}
                    placeholder="Tell us about yourself..."
                    rows={3}
                    className="border-border focus:border-accent focus:ring-accent/20"
                  />
                  {errors.bio && (
                    <p className="text-sm text-destructive">
                      {errors.bio.message}
                    </p>
                  )}
                </div>

                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="location" className="text-card-foreground">
                      Location
                    </Label>
                    <Input
                      id="location"
                      {...register("location")}
                      placeholder="City, Country"
                      className="border-border focus:border-accent focus:ring-accent/20"
                    />
                    {errors.location && (
                      <p className="text-sm text-destructive">
                        {errors.location.message}
                      </p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="website" className="text-card-foreground">
                      Website
                    </Label>
                    <Input
                      id="website"
                      {...register("website")}
                      placeholder="https://example.com"
                      className="border-border focus:border-accent focus:ring-accent/20"
                    />
                    {errors.website && (
                      <p className="text-sm text-destructive">
                        {errors.website.message}
                      </p>
                    )}
                  </div>
                </div>

                {message && (
                  <div
                    className={`p-3 rounded-md text-sm border ${
                      message.type === "success"
                        ? "bg-green-500/10 text-green-700 border-green-500/20"
                        : "bg-red-500/10 text-red-700 border-red-500/20"
                    }`}
                  >
                    {message.text}
                  </div>
                )}

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    disabled={isLoading || updateUser.isPending || !isDirty}
                    className="bg-accent text-accent-foreground hover:bg-accent/90 focus:ring-accent/20 border"
                  >
                    {(isLoading || updateUser.isPending) && (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    )}
                    <Save className="mr-2 h-4 w-4" />
                    {updateUser.isPending ? "Updating..." : "Save Changes"}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </DashboardLayout>
  );
}
