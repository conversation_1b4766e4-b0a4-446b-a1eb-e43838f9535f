import { useTopicNewItemsStore } from "@/stores/topicNewItemsStore";
import { useBusinessSectionStore } from "@/stores/businessSectionStore";
import type { BusinessItemDetail } from "@/types/BusinessSection.types";

/**
 * Mark a single topic as having unseen new items (e.g., when chat/backend pushes a new entry).
 */
export function markTopicHasNew(topicId: string | number) {
  useTopicNewItemsStore.getState().markTopicHasNew(String(topicId));
}

/**
 * Mark multiple topics as having unseen new items in one call.
 */
export function markTopicsHasNew(topicIds: Array<string | number>) {
  useTopicNewItemsStore
    .getState()
    .markTopicsHasNew(topicIds.map((id) => String(id)));
}

/**
 * Clear the new flag for a topic (typically when user opens the topic detail UI).
 */
export function clearTopicNew(topicId: string | number) {
  useTopicNewItemsStore.getState().clearTopicNew(String(topicId));
}

/**
 * Convenience accessor to check if a topic currently has unseen new items.
 */
export function hasNewForTopic(topicId: string | number): boolean {
  return useTopicNewItemsStore.getState().hasNewForTopic(String(topicId));
}

/**
 * Merge newly received entries for a topic into the in-memory store and
 * mark the topic as having new items. Respects the store's per-topic limit.
 */
export function addNewEntriesForTopic(
  sectionId: string,
  topicId: string | number,
  newEntries: BusinessItemDetail[]
) {
  const store = useBusinessSectionStore.getState();
  const idStr = String(topicId);
  const existingEntries = store.getTopicEntries(sectionId, idStr) as any[];

  // Merge by id (store entries take precedence), then append truly new ones
  const map = new Map<string, BusinessItemDetail>();
  existingEntries.forEach((e: any) => map.set(String(e.id), e));
  newEntries.forEach((e) => {
    const key = String((e as any)?.id ?? "");
    if (!map.has(key)) map.set(key, e);
  });

  const merged = Array.from(map.values());
  store.setTopicEntries(sectionId, idStr, merged);
  // Mark as new for the badge/dot
  markTopicHasNew(idStr);
}

/**
 * Variant that locates the section by topicId when sectionId is unknown.
 * If the topic isn't found in any section, no-op.
 */
export function addNewEntriesForTopicViaLookup(
  topicId: string | number,
  newEntries: BusinessItemDetail[]
) {
  const sectionStore = useBusinessSectionStore.getState();
  const found = sectionStore.findTopicItemById(String(topicId)) as {
    sectionId: string;
  } | null;
  if (!found) return;
  addNewEntriesForTopic(found.sectionId, String(topicId), newEntries);
}
