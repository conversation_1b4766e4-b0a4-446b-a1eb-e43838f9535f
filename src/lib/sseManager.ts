// A lightweight manager to enforce a single EventSource per AI chat session
// and provide subscription-based listener management with auto-cleanup on
// page unload. Intended for client-only usage.

type HandlerMap = Record<string, (ev: MessageEvent) => void>;

type SessionEntry = {
  es: EventSource;
  subscriptions: number;
  // For removing listeners cleanly per subscription
  listenerSets: Set<Array<{ type: string; handler: (ev: MessageEvent) => void }>>;
};

class SseManager {
  private sessions: Map<string, SessionEntry> = new Map();
  private beforeUnloadBound = false;

  // Build endpoint for a given session id
  private getEndpoint(sessionId: string): string {
    return `/api/ai-chat/sessions/${sessionId}/events`;
  }

  private ensureBeforeUnloadHook(): void {
    if (this.beforeUnloadBound) return;
    if (typeof window === 'undefined') return;

    try {
      window.addEventListener('beforeunload', () => {
        this.closeAll();
      });
      this.beforeUnloadBound = true;
    } catch {}
  }

  private createOrReuseEventSource(sessionId: string): EventSource {
    const existing = this.sessions.get(sessionId);
    if (existing) {
      const state = (existing.es as any).readyState as number | undefined;
      // 0 = CONNECTING, 1 = OPEN, 2 = CLOSED
      if (state !== 2 /* CLOSED */) {
        return existing.es;
      }
      try { existing.es.close(); } catch {}
      this.sessions.delete(sessionId);
    }

    const es = new EventSource(this.getEndpoint(sessionId), { withCredentials: true });
    const entry: SessionEntry = { es, subscriptions: 0, listenerSets: new Set() };
    this.sessions.set(sessionId, entry);

    // Auto-clean if the stream is closed or errors permanently
    es.addEventListener('error', () => {
      try {
        const rs = (es as any).readyState as number | undefined;
        if (rs === 2 /* CLOSED */) {
          this.close(sessionId);
        }
      } catch {}
    });



    this.ensureBeforeUnloadHook();
    return es;
  }

  subscribe(sessionId: string, handlers: HandlerMap): () => void {
    const es = this.createOrReuseEventSource(sessionId);
    const entry = this.sessions.get(sessionId)!;

    const attached: Array<{ type: string; handler: (ev: MessageEvent) => void }> = [];

    Object.entries(handlers).forEach(([type, handler]) => {
      // Defensive wrapping not needed; consumer handles JSON parsing
      es.addEventListener(type, handler as EventListener);
      attached.push({ type, handler });
    });

    entry.subscriptions += 1;
    entry.listenerSets.add(attached);

    return () => {
      // Remove listeners for this subscription
      attached.forEach(({ type, handler }) => {
        try { es.removeEventListener(type, handler as EventListener); } catch {}
      });

      entry.listenerSets.delete(attached);
      entry.subscriptions = Math.max(0, entry.subscriptions - 1);

      if (entry.subscriptions === 0) {
        this.close(sessionId);
      }
    };
  }

  close(sessionId: string): void {
    const entry = this.sessions.get(sessionId);
    if (!entry) return;

    // Remove all listener sets to avoid dangling references
    entry.listenerSets.forEach((set) => {
      set.forEach(({ type, handler }) => {
        try { entry.es.removeEventListener(type, handler as EventListener); } catch {}
      });
    });
    entry.listenerSets.clear();

    try { entry.es.close(); } catch {}
    this.sessions.delete(sessionId);
  }

  closeAll(): void {
    Array.from(this.sessions.keys()).forEach((sid) => this.close(sid));
  }
}

export const sseManager = new SseManager();


