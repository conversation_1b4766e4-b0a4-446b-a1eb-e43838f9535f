import { QueryClient } from "@tanstack/react-query";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 10 * 60 * 1000, // 10 minutes - data is fresh for longer (was 5 minutes)
      gcTime: 30 * 60 * 1000, // 30 minutes - keep in cache longer (was 10 minutes)
      retry: (failureCount, error: any) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.status >= 400 && error?.status < 500) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      refetchOnWindowFocus: false, // Don't refetch when window regains focus
      refetchOnReconnect: true, // Refetch when internet connection is restored
      refetchOnMount: false, // Don't refetch on mount - use cache first (was true)
    },
    mutations: {
      retry: 1, // Retry mutations once on failure
      onError: (error: any) => {
        // Global error handling for mutations
        console.error("Mutation error:", error);
      },
    },
  },
});

// Query key factory for consistent query keys
export const queryKeys = {
  // User queries
  user: (clerkId?: string) => ["user", clerkId] as const,
  userProfile: (clerkId?: string) => ["user", "profile", clerkId] as const,

  // Project queries
  projects: () => ["projects"] as const,
  project: (id: string) => ["projects", id] as const,
  projectTasks: (projectId: string) =>
    ["projects", projectId, "tasks"] as const,

  // Credits
  credits: () => ["credits"] as const,

  // Admin queries
  admin: {
    analytics: () => ["admin", "analytics"] as const,
    summary: () => ["admin", "analytics", "summary"] as const,
    users: (filters?: any) => ["admin", "users", filters] as const,
    feedback: (filters?: any) => ["admin", "feedback", filters] as const,
  },

  // Business sections
  businessSections: (projectId?: string) =>
    ["business-sections", projectId] as const,
} as const;

// Helper function to invalidate related queries
export const invalidateQueries = {
  user: (clerkId?: string) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.user(clerkId) });
    queryClient.invalidateQueries({ queryKey: queryKeys.userProfile(clerkId) });
  },

  credits: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.credits() });
  },

  projects: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.projects() });
  },

  project: (id: string) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.project(id) });
    queryClient.invalidateQueries({ queryKey: queryKeys.projectTasks(id) });
  },

  admin: () => {
    queryClient.invalidateQueries({ queryKey: queryKeys.admin.analytics() });
    queryClient.invalidateQueries({ queryKey: queryKeys.admin.summary() });
    queryClient.invalidateQueries({ queryKey: queryKeys.admin.users() });
    queryClient.invalidateQueries({ queryKey: queryKeys.admin.feedback() });
  },
};
