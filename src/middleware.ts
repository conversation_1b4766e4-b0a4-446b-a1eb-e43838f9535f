import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

const isProtectedRoute = createRouteMatcher([
  "/user-dashboard(.*)",
  "/admin(.*)",
  "/profile(.*)",
  "/projects(.*)",
  "/settings(.*)",
]);

export default clerkMiddleware(async (auth, req) => {
  const url = new URL(req.url);

  // Require beta gate for protected routes. Allow /auth to render gate client-side.
  const requiresBeta =
    url.pathname.startsWith("/user-dashboard") ||
    url.pathname.startsWith("/admin") ||
    url.pathname.startsWith("/profile") ||
    url.pathname.startsWith("/projects") ||
    url.pathname.startsWith("/settings");

  if (requiresBeta) {
    const betaAccess = req.cookies.get("beta_access")?.value;
    if (betaAccess !== "1") {
      const redirectUrl = new URL("/", req.url);
      return NextResponse.redirect(redirectUrl);
    }
  }

  if (isProtectedRoute(req)) {
    try {
      await auth.protect();
    } catch (e) {
      // Ensure unauthenticated users are redirected to our in-app login page
      const loginUrl = new URL("/auth/login", req.url);
      loginUrl.searchParams.set("redirect_url", url.pathname + url.search);
      return NextResponse.redirect(loginUrl);
    }
  }
});

export const config = {
  matcher: ["/((?!.*\\..*|_next).*)", "/", "/(api|trpc)(.*)"],
};
