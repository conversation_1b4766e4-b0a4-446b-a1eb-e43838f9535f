import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  output: 'standalone',

  // Disable static optimization during Docker builds
  trailingSlash: false,

  // Performance optimizations
  swcMinify: true,
  compress: true,

  // Build optimizations
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
    esmExternals: true,
  },

  // Transpile NextStepjs for ES modules support
  transpilePackages: ['nextstepjs'],

  // Image optimization
  images: {
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.prod.website-files.com',
        port: '',
        pathname: '/**',
      },
    ],
  },

  async rewrites() {
    const backendUrl =
      process.env.NEXT_PUBLIC_ADMIN_API_URL || "http://localhost:3002";

    return [
      {
        source: "/api/admin/:path*",
        destination: `${backendUrl}/admin/:path*`,
      },
      {
        source: "/api/auth/signin",
        destination: `${backendUrl}/auth/signin`,
      },
    ];
  },
};

export default nextConfig;
