## Objects and Types

This reference details important objects, their fields, and expectations across the app.

### Session and Auth

Types: `src/types/Session.types.ts`

- `User`: `{ id, email, name, avatar?, role: 'user'|'admin', isEmailVerified, createdAt, updatedAt }`
- `AuthTokens`: `{ accessToken: string, refreshToken: string, expiresAt: number }`
- `SessionData`: `{ user: User|null, tokens: AuthTokens|null, isAuthenticated: boolean, isInitialized: boolean, pendingEmailVerification?, emailVerificationSent? }`
- `SessionActions`: login/signup/logout/refresh/inits and email verification helpers
- `SessionStore`: `SessionData` + `isLoading`, `error`, `actions: SessionActions`

Token storage: `src/lib/tokenStorage.ts` obfuscates token JSON and stores in `sessionStorage`, optionally also in `localStorage` if long-lived. Access with `TokenStorage.getAccessToken()` for client-side fetches.

### Chat

Types: `src/types/Chat.types.ts`

- `ChatMessage`: `{ id, user, avatar, message, timestamp, isCurrentUser, cta? }`
- `ChatSession`: `{ id, projectId }`
- `ChatStore`: message list, `chatSession`, streaming/loading flags, per-project state maps, and actions for message updates

Event stream: handlers listen for events with payloads like `{ token: string }`, `{ tokens: string[] }`, `{ agent_name, content }`, and derive a printable string chunk from them.

### Business Sections and Items

Types: `src/types/BusinessSection.types.ts`

- `BusinessItem`: topic-level item with id/title and counters
- `BusinessSection`: section holding an array of items (topics)
- `BusinessItemDetail`: a single table row `{ id, title, actions, result, status, description?, createdAt?, updatedAt? }`
- `BusinessSectionStore` and `BusinessItemStore`: see `docs/state-stores.md`

Normalization notes:
- `useAllProjectTopics` produces `ProjectTopicSummary[]` with both the raw `sectionId` and a mapped `mappedCategoryId` and `mappedLabel` derived from backend layer keys (e.g., `problem`, `market-size`). The UI primarily groups by the effective `sectionId = mappedCategoryId ?? sectionId`.

### Topics and Entries

Types: `src/hooks/queries/useTopics.ts`

- `TopicEntry`: `{ id: number, idea?: string, action?: string, result?: string, status?: 'idea'|'action'|'unproven'|'confirmed', position?, metadata? }`
- `ProjectTopicSummary`: fields used to render the grid and infer dependencies and display labels

Create/update payloads to API routes mirror `TopicEntry` but with string keys rather than numeric where needed.


