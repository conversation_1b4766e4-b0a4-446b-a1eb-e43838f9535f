## Architecture

This document covers runtime composition, providers, data flow, and server interaction.

### App composition (providers)

Root providers in `src/app/layout.tsx`:

- `ClerkProvider`: Auth provider for Clerk. Server-side routes acquire JWT via `@clerk/nextjs/server`.
- `QueryProvider`: Wraps the app in TanStack Query client (React Query devtools only in dev).
- `PHProvider`: PostHog analytics provider.
- `ThemeProvider`: Theme toggling (light/dark/system) and class attribute.
- `BackgroundProvider`: UX-only background visual state.
- `SessionProvider`: Initializes the local session store on mount using `TokenStorage` (Zustand `useSessionStore`). Blocks screens with full-screen loading for non-auth pages until `isInitialized`.
- `ClerkSessionProvider`: Bridges Clerk session to client context where needed.

File: `src/app/layout.tsx` shows this exact nesting and where `Toaster` for notifications is injected.

### State and data sources

- Client state: Zustands stores under `src/stores`:
  - `useSessionStore`: Local app session reflecting login/signup/logout, tokens, refresh timers. Tokens are persisted via `TokenStorage` with light obfuscation (XOR+b64). Store exposes `actions` for auth workflow (login, signup, refresh, verify, resend).
  - `useChatStore`: Chat state with messages, current session, per-project streaming/loading flags. Persists messages to `localStorage` by project key.
  - `useBusinessSectionStore`: Holds sections and per-topic entries merged from backend and local edits. Provides CRUD helpers for topic entries.
  - `useBusinessItemStore`: Holds current selected item and its details.
  - `useProjectCreationStore`: Manages onboarding questionnaire and animated creation flow.
  - `useAiIntakeStore`: Tracks ingestion stages and progress log for AI intake sessions.
  - `usePromptStore`: Saves transient prompts in `localStorage` with IDs so flows can be resumed from URL.

- Server data: TanStack Query hooks in `src/hooks/queries` fetch and cache backend resources and invalidate keys on mutations.

### Backend integration and proxying

- All sensitive calls are proxied through Next.js app routes under `src/app/api/*`, which inject the Clerk JWT into Authorization headers. Examples:
  - Projects: `src/app/api/projects/route.ts` (list/create) and `src/app/api/projects/[id]/route.ts` (get/update), forwarding to `NEXT_PUBLIC_ADMIN_API_URL`.
  - Topics: `src/app/api/projects/[id]/topics/route.ts` returns normalized JSON under `{ success, data }` where `data` maintains backend payload. Queries like `useAllProjectTopics` normalize backend variants.
  - Topic entries: `src/app/api/projects/[id]/topics/[topicId]/entries/route.ts` supports GET/POST/PATCH/DELETE, forwarding to backend. The PATCH route accepts `entryId` via query or body and removes it from payload for path semantics.
  - AI chat:
    - Create session: `src/app/api/ai-chat/sessions/route.ts` (POST)
    - Project sessions: `src/app/api/ai-chat/projects/[projectId]/sessions/route.ts` (GET)
    - SSE events: `src/app/api/ai-chat/sessions/[sessionId]/events/route.ts` (GET) streams tokens, context updates, and completion events.
    - Chat to session: `src/app/api/ai-chat/sessions/[sessionId]/chat/route.ts` (POST) proxies and forwards SSE response if returned by backend.

Environment fallbacks:
- `NEXT_PUBLIC_ADMIN_API_URL` or `NEXT_PUBLIC_API_URL` default to `http://localhost:3002` in code paths.
- `STRICT_BACKEND` flags in some routes control fallback behavior (typically error-only).

### Data flow overview

1) User authenticates via mock or Clerk-backed flows. The session store persists tokens through `TokenStorage` and sets up a refresh timer.
2) Pages use Query hooks to request data through internal API routes which attach Clerk JWTs to backend calls.
3) For AI chat, `useAiChat(projectId)` opens an SSE connection to `/api/ai-chat/sessions/:id/events`, listens for tokens, and streams them into the chat store; sending a message uses the `chat` route.
4) Project detail page composes topics and entries from queries and merges entries into `useBusinessSectionStore`, ensuring a single source of truth for the table editor.

### Types and contracts

- Chat: `src/types/Chat.types.ts` defines `ChatMessage` and `ChatStore` interfaces, including optional CTA messages.
- Session: `src/types/Session.types.ts` defines token shape, user shape, session state, and `SessionActions` contract.
- Business Sections: `src/types/BusinessSection.types.ts` includes `BusinessItem`, `BusinessSection`, `BusinessItemDetail` and store contracts.

### UI layout conventions

- Sidebar primitives in `src/components/ui/sidebar.tsx` provide a robust collapsible sidebar with mobile sheet behavior and keyboard toggle. Higher-order component `ProjectSidebar` composes chat and navigation.
- Main content panes rely on Tailwind and motion transitions for smooth state changes.


