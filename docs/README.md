## Siift Frontend Documentation

This documentation explains how to navigate the project, what each part of the codebase does, how state is stored, how the UX flows work, and what objects and API endpoints expect. It links out to deeper docs per topic.

- Read this first: Overview and Navigation (this file)
- Architecture: providers, data flow, and backend proxying → `docs/architecture.md`
- State Stores: how Zustand stores model app state → `docs/state-stores.md`
- Hooks: how to consume and orchestrate data → `docs/hooks.md`
- Components: where the UI lives and how to extend it → `docs/components.md`
- API Routes: server-side routes and contracts → `docs/api.md`
- UX Flows: end-to-end user flows (Chat, Business Sections, Table) → `docs/ux.md`

### Tech stack

- Next.js 15 (app router) + React 19 + TypeScript 5
- UI: Tailwind, shadcn/ui primitives, lucide icons, framer-motion
- State: Zustand (session, chat, project, business sections, prompts)
- Data fetching: TanStack Query for backend resources
- Auth: Clerk (JWT retrieved server-side in API routes)
- Analytics: PostHog

### Directory layout (high signal only)

- `src/app/`: all routes and server handlers (app router)
  - Client pages (e.g. `projects/[id]/page.tsx`)
  - API routes under `src/app/api/...`
- `src/components/`: shareable UI and feature components
- `src/hooks/`: custom hooks (queries, chat, intake, auth)
- `src/stores/`: Zustand stores (session, chat, sections, prompts, etc.)
- `src/lib/`: clients, utils, tokens, constants, analytics
- `src/types/`: shared TypeScript types

### High-level navigation

- Landing and marketing: `src/app/page.tsx`, `src/components/landing/*`
- Auth flows: `src/app/auth/*` (Clerk UI wrapped in app providers)
- User dashboard: `src/app/user-dashboard/page.tsx`
- Projects list and detail: `src/app/projects/*`
  - Project detail page orchestrates the core UX: chat + business sections grid + item detail table
- Admin: `src/app/admin/*` (analytics and system dashboards)

### Core mental model

1) The backend emits structure (Projects, Topics, Topic Entries) and AI chat tokens via SSE.
2) Client-side state keeps the UI snappy (Zustand stores). React Query pulls server data, and API routes proxy requests to the backend with Clerk JWTs.
3) The project detail page composes:
   - Left sidebar: project chat (SSE streaming with `useAiChat`)
   - Main content: Business Sections Grid → click a Topic → Business Item Table editor
   - Header: progress across topics (confirmed entries)

Jump to details:
- Architecture → `docs/architecture.md`
- State (Zustand stores) → `docs/state-stores.md`
- Hooks (chat, intake, topics) → `docs/hooks.md`
- Components (Project pages, UI primitives) → `docs/components.md`
- API routes and contracts → `docs/api.md`
- UX flows and interactions → `docs/ux.md`


