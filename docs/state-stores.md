## State Stores (Zustand)

This app uses multiple focused Zustand stores for clarity and isolation. All stores live in `src/stores/*` and most are wrapped in `zustand/middleware` devtools. Where relevant, stores avoid unnecessary writes by shallow-comparing the next state.

### Session Store (`src/stores/sessionStore.ts`)

Purpose: Manage app-auth session, tokens, refresh lifecycle, and user metadata.

- State: `SessionStore` from `src/types/Session.types.ts`
  - `user`, `tokens`, `isAuthenticated`, `isInitialized`, `isLoading`, `error`, `pendingEmailVerification`, `emailVerificationSent`
- Token lifecycle
  - Tokens saved via `TokenStorage.setTokens(tokens)`. Storage obfuscation with XOR+b64 (development-grade; replace for production-grade encryption).
  - Refresh timer scheduled to run 5 min before expiry, falling back to minimum 1 minute.
  - On refresh failure, store logs the user out.
- Key actions
  - `login(credentials)`: uses `MockAuthAPI` to simulate auth, stores tokens, initializes admin API, schedules refresh.
  - `signup(credentials)`: same as login; sets user and tokens.
  - `logout()`: clears timers and storage and resets to initial state.
  - `initializeSession()`: attempts to restore session from storage; sets `isInitialized` regardless of outcome.
  - `refreshToken()`: refreshes using `MockAuthAPI`, updates tokens, reschedules refresh.
  - `sendEmailVerification(email, name)`, `verifyEmail(email, code)`, `resendEmailVerification(email)`.
  - `updateUser(partial)`, `setTokens(tokens)`, `clearTokens()`.

Related providers:
- `src/components/providers/SessionProvider.tsx` initializes the store on mount and gates non-auth pages with a loading screen until complete.

### Chat Store (`src/stores/chatStore.ts`)

Purpose: Manage per-project chat state, streaming and loading flags, and message history.

- State: `ChatStore` from `src/types/Chat.types.ts`
  - `messages: ChatMessage[]`, `chatSession`, `isLoading`, `isStreaming`
  - Per-project: `projectLoadingStates`, `projectStreamingStates`
- Persistence: messages are saved to `localStorage` under `chat_messages_${projectId}` whenever modified.
- Actions:
  - `setMessages`, `addMessage`
  - `updateLastMessage(content)`: creates or updates a last AI message container to stream into
  - `appendToLastMessage(chunk)`: appends token chunks to last AI message
  - `setChatSession(session)`, `clearChat()`
  - Per-project flags: `setProjectLoading`, `setProjectStreaming`, `getProjectLoading`, `getProjectStreaming`

### Business Section Store (`src/stores/businessSectionStore.ts`)

Purpose: Single source of truth for Sections and Topic Entries used by the table editor and grid.

- State: `sections: BusinessSection[]`, `isLoading`, `error`
- Section-item maintenance: `setSections`, `updateItem`, `addItem`, `removeItem`
- Topic entries CRUD helpers:
  - `getTopicEntries(sectionId, topicId)`
  - `setTopicEntries(sectionId, topicId, entries)`
  - `addTopicEntry(sectionId, topicId, entry)`
  - `updateTopicEntry(sectionId, topicId, entryId, updates)`
  - `removeTopicEntry(sectionId, topicId, entryId)`

Note: On `setTopicEntries`, if the topic item does not exist in the section, it is created on the fly with default metadata so the editor can render.

### Business Item Store (`src/stores/businessItemStore.ts`)

Purpose: Track currently selected business item (topic) and the open table row set.

- State: `selectedItem`, `itemDetails`, `isLoading`, `error`
- Actions: `setSelectedItem`, `setItemDetails`, `addItemDetail`, `updateItemDetail`, `removeItemDetail`

### Project Creation Store (`src/stores/projectCreationStore.ts`)

Purpose: Manage the onboarding questionnaire and creation animation states.

- State: input text, questionnaire flags and answers, step state, animation toggles, created project, error
- Actions: `startQuestionnaire`, `setQuestionnaireAnswer`, `nextQuestion`, `previousQuestion`, `completeQuestionnaire`, `startCreation`, progress setters, animation setters, `setCreatedProject`, `reset`, `setError`

### AI Intake Store (`src/stores/aiIntakeStore.ts`)

Purpose: Track SIIFT ingestion lifecycle for a project via SSE.

- State: `sessionId`, `stage` (`idle` | `streaming_intake` | `ingesting` | `ready`), `progress[]`, `insertedCount`, `error`, `siiftReady`
- Actions: `setSessionId`, `setStage`, `appendProgress`, `setInsertedCount`, `setError`, `setSiiftReady`, `reset`

### Prompt Store (`src/stores/promptStore.ts`)

Purpose: Persist prompt text across navigations and deep links so the app can route to a project and auto-start flows.

- State: `prompts: Record<string, string>`, `lastPromptId`
- Actions: `savePrompt(text) → id`, `getPrompt(id)`, `removePrompt(id)`, `clear()`
- Persistence: SSR-safe localStorage helpers from `src/lib/storage.ts`


