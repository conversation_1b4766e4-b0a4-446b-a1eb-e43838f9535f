## UX Flows

This document explains how key user flows work from UI to data.

### Project Detail Flow

File: `src/app/projects/[id]/page.tsx`

1) Mount
   - Initializes selection to none (`useBusinessItemStore.setSelectedItem(null)`)
   - Resumes AI intake stream if applicable via `useAiIntake.resumeIntake(projectId)` or starts from a `promptId` in URL using `usePromptStore`
2) Data fetch
   - Sections: `useBusinessSections(projectId)` loads high-level sections
   - Topics: `useAllProjectTopics(projectId)` normalizes backend subjects/topics into `[ProjectTopicSummary]`
   - Entries: `useAllTopicEntries(projectId, topics)` fetches entries for all topics concurrently, producing `{ [topicId]: entries }`
   - The page mirrors React Query state into `useBusinessSectionStore`, creating section items per topic and injecting entries for each topic via `setTopicEntries`
3) Render
   - Left: `ProjectSidebar` with embedded chat
   - Top: `ProjectHeader` or `ProjectDetailHeader` if a topic is selected
   - Main: `ProjectMainContent`
     - Grid view: `BusinessSectionsGrid` shows topics grouped as business sections
     - Detail view: clicking a topic shows `BusinessItemTable`
4) Progress
   - Progress is computed as a percentage of topics that have at least one entry with `status === 'confirmed'`

### Business Sections Grid UX

- Displays topics as clickable items; lock/dependency concept is supported by mapping `dependencies` to readable labels.
- Item click selects the topic in `useBusinessItemStore`, switching the main content to the table editor.

### Business Item Table UX

- Inline editing
  - Clicking a cell switches to an input; on blur/enter, the value is saved
  - New rows are created by typing in the bottom "new row" inputs; once any content is entered, a new entry is created, added to the store, and POSTed to API
  - Existing rows auto-calculate status based on the presence of fields:
    - Only idea → `idea`
    - Idea + actions → `action`
    - Idea + actions + result → defaults to `confirmed`, but user may toggle `confirmed`/`unproven` via status select
- Drag & drop
  - Rows can be reordered locally for better thinking flow; server ordering is not yet persisted

### Chat UX

- The left sidebar houses the chat; when collapsed, it shows quick icon controls; when expanded, it renders message history and input.
- Sending a message immediately inserts a user message and flips `projectLoadingStates[projectId]` true during session discovery/creation.
- Before sending, the hook opens SSE and ensures an AI message container to stream into.
- Tokens stream as events and are appended to the last AI message; completion clears `projectStreamingStates[projectId]`.
- Errors post a system message.

### Intake UX

- Intake streams appear as progress lines in `useAiIntakeStore.progress`; the app sets `siiftReady` and invalidates SIIFT queries when `stage === 'ready'`.

### Navigation + Sidebar

- `SidebarProvider` adds keyboard shortcut Cmd/Ctrl+B to toggle the sidebar.
- Mobile shows the sidebar as a Sheet with native back/home controls.
- `ProjectSidebar` adds a resize handle for desktop; it syncs with chat width via parent state.


