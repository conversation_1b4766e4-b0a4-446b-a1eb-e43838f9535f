# NextStepjs Integration in SIIFT Workspace

This document describes the NextStepjs tour integration implemented in the SIIFT workspace to provide an interactive onboarding experience for new users.

## Overview

NextStepjs has been integrated into the SIIFT workspace to create an interactive tour that guides users through the key features of their strategic workspace. The tour automatically starts for new users and can be manually triggered by existing users.

## Features

### Automatic Tour Start
- Tour automatically begins 2 seconds after the workspace loads for new users
- Uses localStorage to remember if a user has seen the tour
- Can be reset for testing or re-showing purposes

### Manual Tour Control
- "Start Tour" button in the workspace header
- But<PERSON> shows different states (active/inactive)
- Tour can be started at any time

### Tour Steps

1. **Welcome to Your Strategic Workspace** - Introduction to the workspace
2. **AI Co-Founder Chat** - Explanation of the left sidebar chat
3. **Progress Tracking** - Overview of the progress bar
4. **Business Framework Sections** - Introduction to the business sections grid
5. **Strategic Topic Items** - Explanation of individual topic items
6. **AI Research & Analysis** - Information about background AI research
7. **Tour Control** - How to restart the tour
8. **Ready to Build Your Business** - Final encouragement and completion

## Implementation Details

### Files Modified

- `src/app/layout.tsx` - Added NextStepjs providers and tour configuration
- `src/components/project/SimulatedOnboardingWorkspace.tsx` - Integrated tour functionality
- `src/hooks/useWorkspaceTour.ts` - Custom hook for tour management
- `src/app/workspace/page.tsx` - New workspace page
- `src/app/globals.css` - Tour styling and element highlights
- `next.config.ts` - ES modules support for NextStepjs

### CSS Classes for Tour Selectors

- `.workspace-chat-left` - Left chat sidebar
- `.workspace-progress` - Progress tracking section
- `.workspace-business-sections` - Business sections grid
- `.workspace-topic-item` - Individual topic items
- `.workspace-ai-status` - AI research status area

### Tour Configuration

The tour is configured in the main layout with:
- 8 comprehensive steps
- Proper positioning and side placement
- Skip functionality for most steps
- Rich content and emojis for engagement

## Usage

### For New Users
1. Navigate to `/workspace`
2. Tour automatically starts after 2 seconds
3. Follow the guided tour through all workspace features
4. Tour completion is saved to localStorage

### For Existing Users
1. Navigate to `/workspace`
2. Click the "Start Tour" button in the header
3. Tour begins immediately
4. Can be skipped or completed at any time

### For Developers
1. Use the `useWorkspaceTour` hook in components
2. Call `resetTour()` to clear tour state for testing
3. Modify tour steps in `src/app/layout.tsx`
4. Add new tour selectors by adding CSS classes

## Customization

### Adding New Tour Steps
1. Add new step objects to the `workspaceTourSteps` array
2. Include proper selector, side, and content
3. Ensure CSS classes exist for new selectors

### Modifying Tour Behavior
1. Edit the `useWorkspaceTour` hook for timing changes
2. Modify the `autoStartTour` function for different delays
3. Update localStorage keys if needed

### Styling Changes
1. Modify CSS in `src/app/globals.css`
2. Update tour element highlights and active states
3. Customize NextStepjs tooltip appearance

## Technical Notes

### Dependencies
- `nextstepjs` - Main tour library
- `motion` - Already included for animations

### Browser Support
- Uses localStorage for tour state persistence
- Compatible with modern browsers
- Responsive design for mobile and desktop

### Performance
- Tour starts after component mount to ensure proper rendering
- Minimal impact on workspace performance
- Efficient state management with custom hook

## Troubleshooting

### Tour Not Starting
1. Check if NextStepjs is properly installed
2. Verify tour configuration in layout.tsx
3. Check browser console for errors
4. Ensure CSS selectors match DOM elements

### Tour Elements Not Highlighted
1. Verify CSS classes are applied to elements
2. Check CSS specificity and conflicts
3. Ensure tour selectors are unique

### Tour State Issues
1. Clear localStorage to reset tour state
2. Check `useWorkspaceTour` hook implementation
3. Verify tour completion logic

## Future Enhancements

### Potential Improvements
1. Add tour progress persistence across sessions
2. Implement tour analytics and completion rates
3. Add contextual tours for different workspace states
4. Create tour templates for different user types
5. Add tour customization based on user preferences

### Integration Opportunities
1. Connect tour completion to user onboarding flow
2. Add tour triggers based on user actions
3. Implement A/B testing for different tour approaches
4. Add tour feedback collection
5. Create tour-based user guidance system
