## API Routes and Contracts

This app proxies backend endpoints through Next.js app routes (`src/app/api/*`), attaching Clerk JWTs server-side. This section documents core routes and expected payloads.

Environment variables used:

- `NEXT_PUBLIC_ADMIN_API_URL` or `NEXT_PUBLIC_API_URL`: Base URL for backend (default `http://localhost:3002` in code)
- `NEXT_PUBLIC_STRICT_BACKEND`: If set to `'false'`, some routes may fall back instead of hard failing (most are strict)

### Auth

Auth is managed client-side by `useSessionStore` and `TokenStorage`. Server routes rely on Clerk for JWT via `auth()`.

### Projects

- List/Create: `src/app/api/projects/route.ts`
  - GET → forwards to `${BACKEND_URL}/api/projects`
  - POST body: `{ name: string, description: string }`
  - Response normalization: `{ success: true, data: Project[] }` when ok

- By ID: `src/app/api/projects/[id]/route.ts`
  - GET, PUT forward to backend; structure similar to above

### Topics

- List topics for a project: `src/app/api/projects/[id]/topics/route.ts`
  - GET with optional `?section_id=...`
  - Returns `{ success: true, data }` where `data` is a backend object or array; the consumer hook `useAllProjectTopics` normalizes it to `ProjectTopicSummary[]`.

### Topic Entries

- `src/app/api/projects/[id]/topics/[topicId]/entries/route.ts`
  - GET: returns `{ success: true, data }` with entries array or wrapped arrays (the consumer normalizes)
  - POST body (create): `{ idea?: string, action?: string, result?: string, status?: 'idea'|'action'|'unproven'|'confirmed' }`
  - PATCH body (update): same shape; query param `entryId` or body field `id` selects the entry; server path takes precedence
  - DELETE query param: `?entryId=...`

### SIIFT Table

- Summary: `src/app/api/projects/[id]/siift-table/route.ts`
- Detailed: `src/app/api/projects/[id]/siift-table/detailed/route.ts`

### AI Chat

- Create session: `src/app/api/ai-chat/sessions/route.ts`
  - POST body: `{ projectId: string }`
  - Returns backend session object; client extracts `session_id` or `id` fields

- Project sessions: `src/app/api/ai-chat/projects/[projectId]/sessions/route.ts` (GET)

- Events (SSE): `src/app/api/ai-chat/sessions/[sessionId]/events/route.ts` (GET)
  - Streams events: `token`, `token_stream` (array), `context_update`, `stream_complete`, `complete`
  - Client uses `EventSource` to append tokens into chat and clear streaming flags on completion

- Chat to session: `src/app/api/ai-chat/sessions/[sessionId]/chat/route.ts` (POST)
  - Body: `{ content: string, agent_stage?: string }` (also accepts `{ message }` for convenience)
  - If backend returns `text/event-stream`, the route forwards the stream to client

### Client API clients

- `src/lib/apiClient.ts`: generic authenticated client using `TokenStorage` access token, with timeout and one-time auth retry by calling `useSessionStore.actions.refreshToken()`.
- `src/lib/admin-api.ts`: admin-only analytics client, token set by `initializeAdminApi(accessToken)` after login or session init.


