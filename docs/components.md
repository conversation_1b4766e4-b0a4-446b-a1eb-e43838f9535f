## Components

This guide highlights the most important UI components and where to extend them.

### Project detail composition

- Page: `src/app/projects/[id]/page.tsx`
  - Uses `SidebarProvider` and renders:
    - `ProjectSidebar`: left column with chat, back/home, and resize handle
    - `ProjectHeader`: top bar with progress and quick actions or `ProjectDetailHeader` in item detail mode
    - `ProjectMainContent`: main area with Business Sections Grid or Business Item Table

### Sidebars

- Primitives: `src/components/ui/sidebar.tsx`
  - `SidebarProvider`, `Sidebar`, `SidebarInset`, and helpers provide a responsive, collapsible, keyboard-toggleable shell.
- Project wrapper: `src/components/project-sidebar.tsx`
  - Manages collapsed vs expanded header content (icons vs full header)
  - Integrates `ProjectChatSidebar` for chat, `ResizeHandle`, and navigation buttons

### Project Header(s)

- `ProjectHeader`: default header with progress bar and quick actions (Priorities, Drafts). When an item is selected, it delegates to `ProjectDetailHeader`.
- `ProjectDetailHeader`: back-to-items button (mobile), and mid area shows `BusinessItemQuestionHeader` with item-specific tips; help popover explains table semantics.

### Main Content

- `ProjectMainContent`:
  - Default: `BusinessSectionsGrid` if no item is selected
  - Detail: `BusinessItemTable` once a topic item is selected
  - Hides Drafts/Files until real data exists

### Business Sections Grid

- `src/components/business-sections/BusinessSectionsGrid.tsx`
  - Merges backend topic entries from `entriesByTopic` with store state via `useBusinessSectionStore.getTopicEntries`
  - Computes dependency/lock state and per-topic status (confirmed if any entry has `status === 'confirmed'`)
  - Emits item click to show table editor

### Business Item Table

- `src/components/business-item-table.tsx`
  - Renders an editable table with drag-and-drop ordering via `@hello-pangea/dnd`
  - Columns: Idea, Action, Result, Status
  - Status behavior:
    - Auto-calculated based on entered fields: idea only → idea; idea+actions → action; idea+actions+result → confirmed
    - Once all fields are filled, user can toggle between confirmed/unproven using a select
  - New row: appears at bottom to add a fresh entry; on save, persists to store and API
  - Existing rows: inline editing and optimistic store update, then PATCH to API
  - Drag and drop: local reorder only (no server ordering persisted yet)

### Chat UI

- `src/components/project-chat-sidebar.tsx`: Chat shell embedded in the left sidebar; wires `useAiChat(projectId)` to UI.
- `src/components/ui/animated-ai-input.tsx` (AI_Prompt): a flexible prompt input with auto-resizing and send/stop actions; emits message on Enter or send button.
- `src/components/ui/chat-bubble.tsx`: bubble primitives (`ChatBubble`, `ChatBubbleMessage`, `ChatBubbleAvatar`, `ChatBubbleAction`) used to render messages and optional actions.

### UI primitives

- `src/components/ui/*`: Button, Input, Select, Table, Tooltip, Popover, Sheet, Skeleton, Tabs, etc. Built on shadcn/ui and Radix.

### SEO/Analytics

- `src/components/seo/*`: `SEOHead` helpers and `StructuredData` to inject JSON-LD.
- `src/components/analytics/PostHogProvider.tsx`: wraps the app with PostHog.

### Providers

- `src/components/providers/*`: ClerkSessionProvider, QueryProvider, SessionProvider.

### Extending components

- To add new table columns, extend the `BusinessItemDetail` type and update `BusinessItemTable` to render/edit them; update API routes accordingly.
- To add new sections or topics, adjust the mapping logic in `useAllProjectTopics` to include new `layer` keys and category labels.
- To customize chat UI/behavior, iterate in `ProjectChatSidebar` and `useAiChat`.


