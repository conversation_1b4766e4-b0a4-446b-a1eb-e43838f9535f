## Hooks

Custom hooks orchestrate backend calls, SSE streams, and store interactions.

### useAuth (`src/hooks/useAuth.ts`)

High-level wrapper over `useSessionStore` actions with UX helpers (toasts and redirects):

- Exposes state: `user`, `isAuthenticated`, `pendingEmailVerification`, `emailVerificationSent`, `error`, `isLoading` (local to hook for operations)
- Actions: `login(credentials)`, `signup(credentials)`, `logout()`, `clearError()`, `sendEmailVerification`, `verifyEmail`, `resendEmailVerification`, `updateUser`, `refreshToken`
- Redirect behavior: routes to `/admin` for admin users else `/user-dashboard`

### useAiChat (`src/hooks/useAiChat.ts`)

Purpose: Chat streaming and session management per project.

- Input: `projectId: string`
- Internals:
  - Maintains `sessionId` and an `EventSource` ref
  - Subscribes to per-project flags from `useChatStore`
  - Discovers/creates a chat session via `/api/ai-chat/projects/${projectId}/sessions` or `/api/ai-chat/sessions`
  - Opens SSE to `/api/ai-chat/sessions/${sessionId}/events`
  - Listens to `token_stream`, `token`, `complete`, `stream_complete`, `error`
  - Ensures an AI message container exists via `updateLastMessage("")` then appends tokens via `appendToLastMessage`
  - Sends messages to `/api/ai-chat/sessions/${sessionId}/chat` with `{ content, agent_stage }`
- Returns: `{ sendMessage, cancelMessage, isLoading, isStreaming, sessionId }`

### useAiIntake (`src/hooks/useAiIntake.ts`)

Purpose: SIIFT ingestion streaming; similar mechanics to `useAiChat` but logs ingestion progress and toggles per-project streaming signals when available.

- Listens to `token_stream`, `token`, `context_update`, `siift_progress`, `complete`
- Appends formatted progress to `useAiIntakeStore.progress` and streams tokens into chat if applicable

### Query hooks (`src/hooks/queries/*.ts`)

Data fetching via React Query, with light normalization for backend variants.

- `useProjects`, `useProject(projectId)`: `authApi.getProjects()` or `/api/projects/:id`
- `useProjectTopics(projectId, sectionId?)`: `/api/projects/:id/topics`
- `useTopicEntries(projectId, topicId)`: `/api/projects/:id/topics/:topicId/entries`
- `useAllProjectTopics(projectId)`: robust parser mapping backend layer keys to frontend labels and category IDs used to build sections; the data is stored as `ProjectTopicSummary[]` with fields: `sectionId`, `topicId`, `numericTopicId`, `title`, `layer`, `dependencies`, `mappedLabel`, `mappedCategory`, `mappedCategoryId`, `dependencyLabels`.
- `useAllTopicEntries(projectId, topics)`: fetches all topic entries concurrently and produces a record `{ [topicId]: TopicEntry[] }` for quick lookup
- Mutations: `useTopicEntryMutations(projectId)` returns `{ createEntry, updateEntry, deleteEntry }` that PATCH/POST/DELETE against API routes and invalidate relevant queries


